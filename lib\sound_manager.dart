import 'dart:math';
import 'dart:typed_data';
import 'dart:io';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

class SoundManager {
  static final AudioPlayer _audioPlayer = AudioPlayer();
  static final AudioPlayer _musicPlayer = AudioPlayer();
  static bool _soundEnabled = true;
  static bool _musicEnabled = true;
  static bool _isMusicPlaying = false;
  static final Map<String, String> _soundPaths = {};
  static String? _backgroundMusicPath;
  static String? _menuMusicPath;
  static const int sampleRate = 44100;
  static const int channels = 1;
  static const int bitsPerSample = 16;
  
  // Initialize sound manager
  static Future<void> initialize({bool soundEnabled = true, bool musicEnabled = true}) async {
    _soundEnabled = soundEnabled;
    _musicEnabled = musicEnabled;
    await _generateAndSaveSounds();
    // Skip music generation for now to avoid app freezing
    // await _generateAndSaveMusic();
  }
  
  // Enable/disable sounds
  static void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
  }
  
  // Enable/disable music
  static void setMusicEnabled(bool enabled) {
    _musicEnabled = enabled;
    if (!enabled && _isMusicPlaying) {
      stopMusic();
    }
  }
  
  // Play a specific sound
  static Future<void> playSound(String soundType) async {
    if (!_soundEnabled) {
      print('Sound disabled, skipping: $soundType');
      return;
    }
    
    try {
      final soundPath = _soundPaths[soundType];
      if (soundPath != null) {
        print('Playing sound: $soundType from $soundPath');
        await _audioPlayer.play(DeviceFileSource(soundPath));
      } else {
        print('Sound path not found for: $soundType, using system sound');
        // Fallback to system sounds
        _playSystemSound(soundType);
      }
    } catch (e) {
      print('Audio player failed for $soundType: $e, using system sound');
      // Fallback to system sounds if audio player fails
      _playSystemSound(soundType);
    }
  }
  
  // Fallback system sounds
  static void _playSystemSound(String soundType) {
    switch (soundType) {
      case 'eat':
        SystemSound.play(SystemSoundType.click);
        HapticFeedback.lightImpact();
        break;
      case 'fruit':
        SystemSound.play(SystemSoundType.click);
        break;
      case 'gameOver':
        SystemSound.play(SystemSoundType.alert);
        HapticFeedback.heavyImpact();
        break;
      case 'levelUp':
        SystemSound.play(SystemSoundType.click);
        HapticFeedback.mediumImpact();
        break;
      case 'buttonClick':
        SystemSound.play(SystemSoundType.click);
        HapticFeedback.selectionClick();
        break;
      case 'gameStart':
        SystemSound.play(SystemSoundType.click);
        HapticFeedback.lightImpact();
        break;
      case 'collision':
        SystemSound.play(SystemSoundType.alert);
        HapticFeedback.heavyImpact();
        break;
      case 'highScore':
        SystemSound.play(SystemSoundType.click);
        HapticFeedback.mediumImpact();
        break;
      case 'move':
        if (Random().nextInt(100) == 0) {
          HapticFeedback.selectionClick();
        }
        break;
    }
  }
  
  // Generate and save sound files
  static Future<void> _generateAndSaveSounds() async {
    try {
      final directory = await getTemporaryDirectory();
      print('Generating sounds in: ${directory.path}');
      
      // Generate eating sound
      final eatSoundData = generateEatingSound();
      final eatFile = File('${directory.path}/eat_sound.wav');
      await eatFile.writeAsBytes(eatSoundData);
      _soundPaths['eat'] = eatFile.path;
      print('Generated eat sound: ${eatFile.path}');
      
      // Generate fruit appear sound
      final fruitSoundData = generateFruitAppearSound();
      final fruitFile = File('${directory.path}/fruit_sound.wav');
      await fruitFile.writeAsBytes(fruitSoundData);
      _soundPaths['fruit'] = fruitFile.path;
      print('Generated fruit sound: ${fruitFile.path}');
      
      // Generate movement sound
      final moveSoundData = generateMovementSound();
      final moveFile = File('${directory.path}/move_sound.wav');
      await moveFile.writeAsBytes(moveSoundData);
      _soundPaths['move'] = moveFile.path;
      print('Generated move sound: ${moveFile.path}');
      
      // Generate game over sound
      final gameOverSoundData = generateGameOverSound();
      final gameOverFile = File('${directory.path}/gameover_sound.wav');
      await gameOverFile.writeAsBytes(gameOverSoundData);
      _soundPaths['gameOver'] = gameOverFile.path;
      
      // Generate level up sound
      final levelUpSoundData = generateLevelUpSound();
      final levelUpFile = File('${directory.path}/levelup_sound.wav');
      await levelUpFile.writeAsBytes(levelUpSoundData);
      _soundPaths['levelUp'] = levelUpFile.path;
      
      // Generate button click sound
      final buttonClickSoundData = generateButtonClickSound();
      final buttonClickFile = File('${directory.path}/button_click_sound.wav');
      await buttonClickFile.writeAsBytes(buttonClickSoundData);
      _soundPaths['buttonClick'] = buttonClickFile.path;
      
      // Generate game start sound
      final gameStartSoundData = generateGameStartSound();
      final gameStartFile = File('${directory.path}/game_start_sound.wav');
      await gameStartFile.writeAsBytes(gameStartSoundData);
      _soundPaths['gameStart'] = gameStartFile.path;
      
      // Generate collision sound
      final collisionSoundData = generateCollisionSound();
      final collisionFile = File('${directory.path}/collision_sound.wav');
      await collisionFile.writeAsBytes(collisionSoundData);
      _soundPaths['collision'] = collisionFile.path;
      
      // Generate high score sound
      final highScoreSoundData = generateHighScoreSound();
      final highScoreFile = File('${directory.path}/high_score_sound.wav');
      await highScoreFile.writeAsBytes(highScoreSoundData);
      _soundPaths['highScore'] = highScoreFile.path;
      
    } catch (e) {
      // If file generation fails, we'll use system sounds
      print('Sound generation failed: $e');
    }
  }
  
  // Dispose resources
  // Play background music during gameplay
  static Future<void> playBackgroundMusic() async {
    if (!_musicEnabled || _isMusicPlaying) return;
    
    try {
      if (_backgroundMusicPath != null) {
        await _musicPlayer.setSource(DeviceFileSource(_backgroundMusicPath!));
        await _musicPlayer.setReleaseMode(ReleaseMode.loop);
        await _musicPlayer.setVolume(0.3); // Lower volume for background
        await _musicPlayer.resume();
        _isMusicPlaying = true;
      }
    } catch (e) {
      print('Background music play failed: $e');
    }
  }
  
  // Play menu music
  static Future<void> playMenuMusic() async {
    if (!_musicEnabled) return;
    
    try {
      if (_menuMusicPath != null) {
        await _musicPlayer.setSource(DeviceFileSource(_menuMusicPath!));
        await _musicPlayer.setReleaseMode(ReleaseMode.loop);
        await _musicPlayer.setVolume(0.4); // Slightly higher for menu
        await _musicPlayer.resume();
        _isMusicPlaying = true;
      }
    } catch (e) {
      print('Menu music play failed: $e');
    }
  }
  
  // Stop music
  static Future<void> stopMusic() async {
    try {
      await _musicPlayer.stop();
      _isMusicPlaying = false;
    } catch (e) {
      print('Music stop failed: $e');
    }
  }
  
  // Pause music
  static Future<void> pauseMusic() async {
    try {
      await _musicPlayer.pause();
    } catch (e) {
      print('Music pause failed: $e');
    }
  }
  
  // Resume music
  static Future<void> resumeMusic() async {
    if (!_musicEnabled) return;
    
    try {
      await _musicPlayer.resume();
    } catch (e) {
      print('Music resume failed: $e');
    }
  }

  static Future<void> dispose() async {
    await _audioPlayer.dispose();
    await _musicPlayer.dispose();
  }

  // Generate eating sound (classic snake game chomp)
  static Uint8List generateEatingSound() {
    const duration = 0.15; // 150ms - short and snappy like classic snake games
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create classic "chomp" sound like in old snake games
      // Quick ascending then descending frequency sweep
      double progress = t / duration;
      double frequency = 300 + (sin(progress * pi * 2) * 150); // Sweep from 300-450Hz
      
      // Sharp attack and quick decay envelope
      double envelope = exp(-t * 12) * (1 - progress * 0.7);
      
      // Main chomp tone
      double chomp = sin(2 * pi * frequency * t) * envelope;
      
      // Add slight harmonic for character
      double harmonic = sin(2 * pi * frequency * 1.5 * t) * envelope * 0.2;
      
      // Quick "bite" click at the start
      double bite = (t < 0.01) ? sin(2 * pi * 2000 * t) * (1 - t / 0.01) * 0.3 : 0;
      
      double sample = (chomp + harmonic + bite) * 0.8;
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Generate fruit appear sound (pop)
  static Uint8List generateFruitAppearSound() {
    const duration = 0.2; // 200ms
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create pop sound with quick attack and decay
      double envelope = exp(-t * 15); // Very fast decay
      double pop = sin(2 * pi * 1200 * t) * envelope; // High frequency pop
      double bass = sin(2 * pi * 200 * t) * envelope * 0.3; // Low frequency thump
      
      double sample = (pop + bass) * 0.6;
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Generate snake movement sound (subtle slither)
  static Uint8List generateMovementSound() {
    const duration = 0.1; // 100ms - very short
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);
    final random = Random();

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create subtle slithering sound
      double noise = (random.nextDouble() - 0.5) * 0.1; // Very quiet noise
      double slither = sin(2 * pi * 150 * t) * 0.05; // Low frequency
      double envelope = sin(pi * t / duration); // Smooth envelope
      
      double sample = (noise + slither) * envelope * 0.2; // Very quiet
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Generate game over sound (dramatic descending tone)
  static Uint8List generateGameOverSound() {
    const duration = 1.0; // 1 second
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create dramatic descending sound
      double frequency = 400 - (t * 300); // Descend from 400Hz to 100Hz
      double envelope = exp(-t * 2); // Slow decay
      double tone = sin(2 * pi * frequency * t) * envelope;
      double bass = sin(2 * pi * (frequency / 2) * t) * envelope * 0.5;
      
      double sample = (tone + bass) * 0.7;
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Generate level up sound (ascending celebratory tone)
  static Uint8List generateLevelUpSound() {
    const duration = 0.8; // 800ms
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create ascending celebratory sound
      double frequency = 300 + (t * 500); // Ascend from 300Hz to 800Hz
      double envelope = sin(pi * t / duration); // Bell curve envelope
      double tone = sin(2 * pi * frequency * t) * envelope;
      double harmony = sin(2 * pi * (frequency * 1.5) * t) * envelope * 0.3;
      
      double sample = (tone + harmony) * 0.8;
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Generate button click sound
  static Uint8List generateButtonClickSound() {
    const duration = 0.1; // 100ms
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create sharp click sound
      double envelope = exp(-t * 30); // Very fast decay
      double click = sin(2 * pi * 1000 * t) * envelope;
      
      double sample = click * 0.5;
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Generate game start sound (uplifting tone)
  static Uint8List generateGameStartSound() {
    const duration = 0.5; // 500ms
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create uplifting start sound
      double frequency = 400 + (t * 200); // Ascend from 400Hz to 600Hz
      double envelope = sin(pi * t / duration); // Bell curve envelope
      double tone = sin(2 * pi * frequency * t) * envelope;
      double harmony = sin(2 * pi * (frequency * 0.75) * t) * envelope * 0.4;
      
      double sample = (tone + harmony) * 0.6;
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Generate collision sound (harsh impact)
  static Uint8List generateCollisionSound() {
    const duration = 0.4; // 400ms
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);
    final random = Random();

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create harsh collision sound
      double noise = (random.nextDouble() - 0.5) * 2.0;
      double envelope = exp(-t * 8); // Fast decay
      double crash = sin(2 * pi * 200 * t) * envelope; // Low frequency crash
      double impact = sin(2 * pi * 800 * t) * envelope * 0.5; // Higher frequency impact
      
      double sample = (noise * 0.8 + crash + impact) * envelope * 0.8;
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Generate high score sound (triumphant chime)
  static Uint8List generateHighScoreSound() {
    const duration = 0.6; // 600ms
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create triumphant chime sound
      double frequency1 = 523; // C5
      double frequency2 = 659; // E5
      double frequency3 = 784; // G5
      
      double envelope = sin(pi * t / duration); // Bell curve envelope
      double tone1 = sin(2 * pi * frequency1 * t) * envelope * 0.4;
      double tone2 = sin(2 * pi * frequency2 * t) * envelope * 0.3;
      double tone3 = sin(2 * pi * frequency3 * t) * envelope * 0.3;
      
      double sample = (tone1 + tone2 + tone3) * 0.8;
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Generate and save background music
  static Future<void> _generateAndSaveMusic() async {
    try {
      final directory = await getTemporaryDirectory();
      print('Generating music files...');
      
      // Generate background music (20 minutes)
      print('Generating background music (this may take a while)...');
      final backgroundMusicData = generateBackgroundMusic();
      final backgroundMusicFile = File('${directory.path}/background_music.wav');
      await backgroundMusicFile.writeAsBytes(backgroundMusicData);
      _backgroundMusicPath = backgroundMusicFile.path;
      print('Background music generated: ${backgroundMusicFile.path}');
      
      // Generate menu music (5 minutes)
      print('Generating menu music...');
      final menuMusicData = generateMenuMusic();
      final menuMusicFile = File('${directory.path}/menu_music.wav');
      await menuMusicFile.writeAsBytes(menuMusicData);
      _menuMusicPath = menuMusicFile.path;
      print('Menu music generated: ${menuMusicFile.path}');
      
      print('All music files generated successfully');
    } catch (e) {
      print('Music generation failed: $e');
      // Continue without music
    }
  }

  // Generate background music (2 minutes for testing, will be longer in final version)
  static Uint8List generateBackgroundMusic() {
    const duration = 120.0; // 2 minutes for testing
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);
    final random = Random();

    // Musical scales and chord progressions
    final List<double> scale = [261.63, 293.66, 329.63, 349.23, 392.00, 440.00, 493.88]; // C major scale
    final List<List<int>> chordProgression = [
      [0, 2, 4], // C major
      [5, 0, 2], // A minor
      [3, 5, 0], // F major
      [4, 6, 1], // G major
    ];

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create different sections for variety (each section is 4 minutes)
      int section = (t / 240).floor() % 5; // 5 different sections
      
      // Determine current chord (changes every 6 seconds for more variety)
      int chordIndex = ((t / 6).floor()) % chordProgression.length;
      List<int> currentChord = chordProgression[chordIndex];
      
      // Variable tempo based on section
      double tempo = 0.6 + (section * 0.1); // 36-60 BPM
      double beatTime = (t * tempo) % 1.0;
      double beatEnvelope = exp(-beatTime * 4) * 0.4;
      
      // Main chord progression
      double chord = 0;
      for (int note in currentChord) {
        double freq = scale[note] * (0.4 + section * 0.1); // Varying octaves
        chord += sin(2 * pi * freq * t + sin(t * 0.1)) * 0.25;
      }
      
      // Dynamic melody that changes with sections
      int melodyNote = currentChord[(t * (0.3 + section * 0.1)).floor() % currentChord.length];
      double melodyFreq = scale[melodyNote] * (1.5 + section * 0.3);
      double melodyIntensity = 0.15 + (sin(t * 0.05) * 0.1);
      double melody = sin(2 * pi * melodyFreq * t + cos(t * 0.2)) * melodyIntensity;
      
      // Evolving ambient pad
      double pad = 0;
      for (int j = 0; j < 3; j++) {
        double padFreq = scale[currentChord[j]] * (0.2 + section * 0.05);
        double phase = t * 0.1 + j * 2.1 + sin(t * 0.03) * 0.5;
        pad += sin(2 * pi * padFreq * t + phase) * 0.12;
      }
      
      // Rhythmic elements that vary by section
      double rhythm = 0;
      if (section >= 2) { // Add rhythm from section 2 onwards
        if (beatTime < 0.15) {
          rhythm = (random.nextDouble() - 0.5) * beatEnvelope * (0.1 + section * 0.02);
        }
        // Add subtle bass drum
        if (beatTime < 0.05) {
          rhythm += sin(2 * pi * 60 * t) * exp(-beatTime * 20) * 0.2;
        }
      }
      
      // Atmospheric effects
      double atmosphere = 0;
      if (section >= 1) {
        atmosphere = sin(2 * pi * (scale[0] * 0.125) * t + sin(t * 0.02) * 2) * 0.08;
        atmosphere += sin(2 * pi * (scale[4] * 0.125) * t + cos(t * 0.03) * 1.5) * 0.06;
      }
      
      // Mix all elements with section-based intensity
      double intensity = 0.3 + (section * 0.1) + (sin(t * 0.01) * 0.1);
      double sample = (chord + melody + pad + rhythm + atmosphere) * intensity;
      
      // Apply overall envelope and dynamic volume changes
      double overallEnvelope = 1.0;
      if (t < 8.0) overallEnvelope = t / 8.0; // Longer fade in
      if (t > duration - 8.0) overallEnvelope = (duration - t) / 8.0; // Longer fade out
      
      // Add subtle volume variations for organic feel
      double volumeVariation = 1.0 + (sin(t * 0.007) * 0.1);
      sample *= overallEnvelope * volumeVariation;
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Generate menu music (1 minute for testing)
  static Uint8List generateMenuMusic() {
    const duration = 60.0; // 1 minute for testing
    final samples = (sampleRate * duration).round();
    final data = Uint8List(samples * 2);

    // Upbeat chord progression
    final List<double> scale = [261.63, 293.66, 329.63, 349.23, 392.00, 440.00, 493.88];
    final List<List<int>> chordProgression = [
      [0, 2, 4], // C major
      [4, 6, 1], // G major
      [5, 0, 2], // A minor
      [3, 5, 0], // F major
    ];

    for (int i = 0; i < samples; i++) {
      double t = i / sampleRate;
      
      // Create different parts for variety (each part is 1 minute)
      int part = (t / 60).floor() % 5;
      
      // Dynamic chord changes (every 3-5 seconds based on part)
      double chordChangeRate = 3.0 + part * 0.5;
      int chordIndex = ((t / chordChangeRate).floor()) % chordProgression.length;
      List<int> currentChord = chordProgression[chordIndex];
      
      // Variable tempo (100-140 BPM based on part)
      double bpm = 100 + (part * 10);
      double beatTime = (t * (bpm / 60)) % 1.0;
      double beatEnvelope = exp(-beatTime * 6) * 0.5;
      
      // Main chord with variations
      double chord = 0;
      for (int note in currentChord) {
        double freq = scale[note] * (0.8 + part * 0.1);
        double phase = t + sin(t * 0.1) * 0.2; // Slight modulation
        chord += sin(2 * pi * freq * phase) * 0.3;
      }
      
      // Dynamic melody that gets more complex
      int melodyNote = currentChord[(t * (1 + part * 0.3)).floor() % currentChord.length];
      double melodyFreq = scale[melodyNote] * (1.5 + part * 0.2);
      double melodyModulation = sin(t * 0.3) * 0.5;
      double melody = sin(2 * pi * melodyFreq * t + melodyModulation) * 0.25;
      
      // Add harmony in later parts
      if (part >= 2) {
        int harmonyNote = currentChord[(melodyNote + 2) % currentChord.length];
        double harmonyFreq = scale[harmonyNote] * 2;
        melody += sin(2 * pi * harmonyFreq * t + melodyModulation * 0.7) * 0.15;
      }
      
      // Energetic bass line
      double bassFreq = scale[currentChord[0]] * 0.5;
      double bassPattern = sin(2 * pi * bassFreq * t) * beatEnvelope;
      if (part >= 1) {
        // Add syncopated bass in later parts
        bassPattern += sin(2 * pi * bassFreq * 1.5 * t) * beatEnvelope * 0.3;
      }
      
      // Percussion elements
      double percussion = 0;
      if (part >= 1) {
        // Kick drum
        if (beatTime < 0.1) {
          percussion += sin(2 * pi * 60 * t) * exp(-beatTime * 15) * 0.4;
        }
        // Hi-hat
        if ((beatTime + 0.5) % 1.0 < 0.05) {
          percussion += (Random().nextDouble() - 0.5) * 0.2;
        }
      }
      
      // Lead synth in final parts
      double lead = 0;
      if (part >= 3) {
        double leadFreq = scale[(currentChord[0] + 4) % scale.length] * 4;
        lead = sin(2 * pi * leadFreq * t + sin(t * 2) * 0.3) * 0.2 * sin(t * 0.1);
      }
      
      // Mix all elements with dynamic intensity
      double intensity = 0.4 + (part * 0.1) + (sin(t * 0.05) * 0.1);
      double sample = (chord + melody + bassPattern + percussion + lead) * intensity;
      
      // Overall envelope with smooth transitions
      double overallEnvelope = 1.0;
      if (t < 3.0) overallEnvelope = t / 3.0; // Fade in
      if (t > duration - 3.0) overallEnvelope = (duration - t) / 3.0; // Fade out
      
      // Add excitement with subtle volume pumping
      double pumping = 1.0 + (sin(t * (bpm / 60) * 2 * pi) * 0.05);
      sample *= overallEnvelope * pumping;
      
      // Convert to 16-bit
      int value = (sample * 32767).round().clamp(-32768, 32767);
      data[i * 2] = value & 0xFF;
      data[i * 2 + 1] = (value >> 8) & 0xFF;
    }

    return _createWavFile(data, samples);
  }

  // Create WAV file format
  static Uint8List _createWavFile(Uint8List audioData, int samples) {
    final header = ByteData(44);
    
    // RIFF header
    header.setUint32(0, 0x46464952, Endian.little); // "RIFF"
    header.setUint32(4, 36 + audioData.length, Endian.little); // File size - 8
    header.setUint32(8, 0x45564157, Endian.little); // "WAVE"
    
    // Format chunk
    header.setUint32(12, 0x20746d66, Endian.little); // "fmt "
    header.setUint32(16, 16, Endian.little); // Chunk size
    header.setUint16(20, 1, Endian.little); // Audio format (PCM)
    header.setUint16(22, channels, Endian.little); // Channels
    header.setUint32(24, sampleRate, Endian.little); // Sample rate
    header.setUint32(28, sampleRate * channels * bitsPerSample ~/ 8, Endian.little); // Byte rate
    header.setUint16(32, channels * bitsPerSample ~/ 8, Endian.little); // Block align
    header.setUint16(34, bitsPerSample, Endian.little); // Bits per sample
    
    // Data chunk
    header.setUint32(36, 0x61746164, Endian.little); // "data"
    header.setUint32(40, audioData.length, Endian.little); // Data size
    
    // Combine header and data
    final result = Uint8List(44 + audioData.length);
    result.setRange(0, 44, header.buffer.asUint8List());
    result.setRange(44, 44 + audioData.length, audioData);
    
    return result;
  }
}