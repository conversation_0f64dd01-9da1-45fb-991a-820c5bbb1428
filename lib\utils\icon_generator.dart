import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class IconGenerator {
  /// Generate a default snake game icon programmatically
  static Future<Uint8List> generateDefaultIcon({int size = 1024}) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint();
    
    // Background gradient
    final backgroundGradient = RadialGradient(
      colors: [
        const Color(0xFF2E7D32), // Dark green
        const Color(0xFF1B5E20), // Darker green
        const Color(0xFF0D2818), // Very dark green
      ],
    );
    
    final backgroundPaint = Paint()
      ..shader = backgroundGradient.createShader(
        Rect.fromLTWH(0, 0, size.toDouble(), size.toDouble()),
      );
    
    // Draw background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.toDouble(), size.toDouble()),
      backgroundPaint,
    );
    
    // Draw snake body
    final snakePaint = Paint()
      ..color = const Color(0xFF4CAF50)
      ..style = PaintingStyle.fill;
    
    final snakeStrokePaint = Paint()
      ..color = const Color(0xFF2E7D32)
      ..style = PaintingStyle.stroke
      ..strokeWidth = size * 0.02;
    
    // Snake segments (spiral pattern)
    final center = Offset(size / 2, size / 2);
    final segments = <Offset>[];
    
    // Create spiral snake
    for (int i = 0; i < 12; i++) {
      final angle = i * 0.8;
      final radius = (size * 0.15) + (i * size * 0.02);
      final x = center.dx + radius * cos(angle);
      final y = center.dy + radius * sin(angle);
      segments.add(Offset(x, y));
    }
    
    // Draw snake segments
    for (int i = 0; i < segments.length; i++) {
      final segmentSize = size * 0.08 - (i * size * 0.003);
      
      // Body segment
      canvas.drawCircle(segments[i], segmentSize, snakePaint);
      canvas.drawCircle(segments[i], segmentSize, snakeStrokePaint);
      
      // Highlight
      final highlightPaint = Paint()
        ..color = const Color(0xFF66BB6A)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        segments[i] + Offset(-segmentSize * 0.3, -segmentSize * 0.3),
        segmentSize * 0.3,
        highlightPaint,
      );
    }
    
    // Draw snake head (larger, with eyes)
    if (segments.isNotEmpty) {
      final headPos = segments.first;
      final headSize = size * 0.1;
      
      // Head
      canvas.drawCircle(headPos, headSize, snakePaint);
      canvas.drawCircle(headPos, headSize, snakeStrokePaint);
      
      // Eyes
      final eyePaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      
      final pupilPaint = Paint()
        ..color = Colors.black
        ..style = PaintingStyle.fill;
      
      // Left eye
      final leftEye = headPos + Offset(-headSize * 0.3, -headSize * 0.2);
      canvas.drawCircle(leftEye, headSize * 0.15, eyePaint);
      canvas.drawCircle(leftEye, headSize * 0.08, pupilPaint);
      
      // Right eye
      final rightEye = headPos + Offset(headSize * 0.3, -headSize * 0.2);
      canvas.drawCircle(rightEye, headSize * 0.15, eyePaint);
      canvas.drawCircle(rightEye, headSize * 0.08, pupilPaint);
    }
    
    // Draw food (apple)
    final foodPos = Offset(size * 0.75, size * 0.25);
    final foodSize = size * 0.08;
    
    // Apple body
    final applePaint = Paint()
      ..color = const Color(0xFFE53935)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(foodPos, foodSize, applePaint);
    
    // Apple highlight
    final appleHighlightPaint = Paint()
      ..color = const Color(0xFFFF5722)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(
      foodPos + Offset(-foodSize * 0.3, -foodSize * 0.3),
      foodSize * 0.4,
      appleHighlightPaint,
    );
    
    // Apple stem
    final stemPaint = Paint()
      ..color = const Color(0xFF4CAF50)
      ..style = PaintingStyle.stroke
      ..strokeWidth = size * 0.01
      ..strokeCap = StrokeCap.round;
    
    canvas.drawLine(
      foodPos + Offset(0, -foodSize),
      foodPos + Offset(0, -foodSize * 1.3),
      stemPaint,
    );
    
    // Convert to image
    final picture = recorder.endRecording();
    final img = await picture.toImage(size, size);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }
}
