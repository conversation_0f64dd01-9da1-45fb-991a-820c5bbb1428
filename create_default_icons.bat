@echo off
echo Creating default Flutter icons...

echo Creating temporary Flutter project to get default icons...
cd ..
flutter create temp_project
cd temp_project

echo Copying default icons to our project...
copy "android\app\src\main\res\mipmap-mdpi\ic_launcher.png" "..\arzasnake\android\app\src\main\res\mipmap-mdpi\ic_launcher.png"
copy "android\app\src\main\res\mipmap-hdpi\ic_launcher.png" "..\arzasnake\android\app\src\main\res\mipmap-hdpi\ic_launcher.png"
copy "android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" "..\arzasnake\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png"
copy "android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" "..\arzasnake\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png"
copy "android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" "..\arzasnake\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png"

echo Cleaning up...
cd ..
rmdir /s /q temp_project

cd arzasnake
echo Default icons restored!
pause
