import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../sound_manager.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool soundEnabled = true;
  bool musicEnabled = true;
  bool vibrationEnabled = true;
  double gameSpeed = 1.0;
  String difficulty = 'Medium';
  
  @override
  void initState() {
    super.initState();
    // Load current settings from global settings if available
    // You can implement SharedPreferences here for persistence
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1B5E20),
      appBar: AppBar(
        title: const Text(
          'Settings',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF2E7D32),
              Color(0xFF1B5E20),
              Color(0xFF0D2818),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              // Sound Settings Card
              _buildSettingsCard(
                title: 'Sound & Vibration',
                icon: Icons.volume_up,
                children: [
                  _buildSwitchTile(
                    title: 'Enable Sounds',
                    subtitle: 'Game sounds and effects',
                    value: soundEnabled,
                    onChanged: (value) async {
                      setState(() {
                        soundEnabled = value;
                      });
                      SoundManager.setSoundEnabled(value);
                      if (value) {
                        try {
                          await SoundManager.playSound('buttonClick');
                        } catch (e) {
                          SystemSound.play(SystemSoundType.click);
                        }
                      }
                    },
                  ),
                  _buildSwitchTile(
                    title: 'Enable Music',
                    subtitle: 'Background music during gameplay',
                    value: musicEnabled,
                    onChanged: (value) async {
                      setState(() {
                        musicEnabled = value;
                      });
                      SoundManager.setMusicEnabled(value);
                      if (value) {
                        try {
                          await SoundManager.playSound('buttonClick');
                        } catch (e) {
                          SystemSound.play(SystemSoundType.click);
                        }
                      }
                    },
                  ),
                  _buildSwitchTile(
                    title: 'Enable Vibration',
                    subtitle: 'Vibrate on eating and collision',
                    value: vibrationEnabled,
                    onChanged: (value) {
                      setState(() {
                        vibrationEnabled = value;
                      });
                      if (value) {
                        HapticFeedback.lightImpact();
                      }
                    },
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Game Settings Card
              _buildSettingsCard(
                title: 'Game Settings',
                icon: Icons.gamepad,
                children: [
                  _buildSliderTile(
                    title: 'Game Speed',
                    subtitle: 'Control snake movement speed (${_getSpeedLabel(gameSpeed)})',
                    value: gameSpeed,
                    min: 0.3,
                    max: 3.0,
                    divisions: 12,
                    onChanged: (value) async {
                      setState(() {
                        gameSpeed = value;
                      });
                      // Provide immediate feedback
                      HapticFeedback.selectionClick();
                      try {
                        await SoundManager.playSound('buttonClick');
                      } catch (e) {
                        // Continue if sound fails
                      }
                    },
                  ),
                  _buildDropdownTile(
                    title: 'Difficulty Level',
                    subtitle: 'Choose your preferred difficulty',
                    value: difficulty,
                    items: ['Easy', 'Medium', 'Hard', 'Expert'],
                    onChanged: (value) async {
                      setState(() {
                        difficulty = value!;
                      });
                      try {
                        await SoundManager.playSound('buttonClick');
                      } catch (e) {
                        // Continue if sound fails
                      }
                    },
                  ),
                ],
              ),
              
              const SizedBox(height: 30),
              
              // Save Button
              Container(
                width: double.infinity,
                height: 60,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
                  ),
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ElevatedButton(
                  onPressed: () {
                    // Provide haptic feedback
                    HapticFeedback.mediumImpact();
                    
                    // Show confirmation
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Settings saved! Speed: ${_getSpeedLabel(gameSpeed)}'),
                        backgroundColor: Colors.green,
                        duration: const Duration(seconds: 1),
                      ),
                    );
                    
                    // Save settings and return
                    Navigator.pop(context, {
                      'soundEnabled': soundEnabled,
                      'musicEnabled': musicEnabled,
                      'vibrationEnabled': vibrationEnabled,
                      'gameSpeed': gameSpeed,
                      'difficulty': difficulty,
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                    shadowColor: Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  child: const Text(
                    'Save Settings',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(15),
                topRight: Radius.circular(15),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: Colors.white, size: 24),
                const SizedBox(width: 10),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.7),
          fontSize: 14,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFF4CAF50),
        activeTrackColor: const Color(0xFF4CAF50).withValues(alpha: 0.5),
      ),
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required ValueChanged<double> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.all(15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 10),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: const Color(0xFF4CAF50),
              inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
              thumbColor: const Color(0xFF4CAF50),
              overlayColor: const Color(0xFF4CAF50).withValues(alpha: 0.2),
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: divisions,
              label: '${value.toStringAsFixed(1)}x',
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.all(15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
              ),
            ),
            child: DropdownButton<String>(
              value: value,
              isExpanded: true,
              dropdownColor: const Color(0xFF2E7D32),
              style: const TextStyle(color: Colors.white),
              underline: Container(),
              items: items.map((String item) {
                return DropdownMenuItem<String>(
                  value: item,
                  child: Text(item),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  String _getSpeedLabel(double speed) {
    if (speed <= 0.5) return 'Very Slow';
    if (speed <= 0.8) return 'Slow';
    if (speed <= 1.0) return 'Normal';
    if (speed <= 1.3) return 'Fast';
    if (speed <= 1.7) return 'Very Fast';
    if (speed <= 2.2) return 'Ultra Fast';
    return 'Lightning';
  }
}