Write-Host "Converting new snake game icon..." -ForegroundColor Green

# Load System.Drawing
Add-Type -AssemblyName System.Drawing

# Load source image
$sourcePath = "assets\icons\snake_game_icon_3.png"
$sourceImage = [System.Drawing.Image]::FromFile((Resolve-Path $sourcePath).Path)

Write-Host "Original size: $($sourceImage.Width)x$($sourceImage.Height)" -ForegroundColor Yellow

# Define all Android icon sizes
$iconSizes = @{
    "android\app\src\main\res\mipmap-mdpi\ic_launcher.png" = 48
    "android\app\src\main\res\mipmap-hdpi\ic_launcher.png" = 72
    "android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" = 96
    "android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" = 144
    "android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" = 192
}

foreach ($outputPath in $iconSizes.Keys) {
    $size = $iconSizes[$outputPath]
    
    # Create resized bitmap
    $resizedBitmap = New-Object System.Drawing.Bitmap($size, $size)
    $graphics = [System.Drawing.Graphics]::FromImage($resizedBitmap)
    $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
    $graphics.DrawImage($sourceImage, 0, 0, $size, $size)
    
    # Save as PNG
    $resizedBitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    Write-Host "Created: $outputPath ($($size)x$($size))" -ForegroundColor Green
    
    # Cleanup
    $graphics.Dispose()
    $resizedBitmap.Dispose()
}

# Cleanup source image
$sourceImage.Dispose()

Write-Host "New icon conversion completed!" -ForegroundColor Green
