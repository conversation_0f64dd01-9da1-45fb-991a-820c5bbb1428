import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math';
import 'dart:ui';
import 'package:audioplayers/audioplayers.dart';
import 'screens/settings_screen.dart';
import 'screens/levels_screen.dart';
import 'screens/game_over_screen.dart';
import 'models/level_data.dart';
import 'painters/obstacle_painter.dart';

void main() {
  runApp(const ArzaSnakeApp());
}

class ArzaSnakeApp extends StatelessWidget {
  const ArzaSnakeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Snake Game',
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Arial',
      ),
      home: const MainMenuScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class MainMenuScreen extends StatefulWidget {
  const MainMenuScreen({super.key});

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen> {
  // Global game settings
  static GameSettings globalGameSettings = GameSettings();



  void _applySettings(Map<String, dynamic> settings) {
    setState(() {
      globalGameSettings = GameSettings(
        soundEnabled: settings['soundEnabled'] ?? true,
        vibrationEnabled: settings['vibrationEnabled'] ?? true,
        gameSpeed: settings['gameSpeed'] ?? 1.0,
        difficulty: settings['difficulty'] ?? 'Medium',
      );
    });
    
    // Save settings to persistent storage (you can implement SharedPreferences here)
    print('Settings applied: Speed=${globalGameSettings.gameSpeed}, Difficulty=${globalGameSettings.difficulty}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF4CAF50),
              Color(0xFF2E7D32),
              Color(0xFF1B5E20),
              Color(0xFF0D2818),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Game Title
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Text(
                      '🐍',
                      style: TextStyle(
                        fontSize: 80,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.5),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Text(
                      'Snake Game',
                      style: TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black54,
                            blurRadius: 10,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      'Classic Snake Adventure',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.white.withValues(alpha: 0.9),
                        fontWeight: FontWeight.w300,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 50),
              
              // Menu Buttons
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Column(
                  children: [
                    _buildMenuButton(
                      context,
                      title: 'Quick Play',
                      icon: Icons.play_arrow,
                      onTap: () {
                        SystemSound.play(SystemSoundType.click);
                        HapticFeedback.selectionClick();
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => GameScreen(gameSettings: globalGameSettings),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 20),
                    
                    _buildMenuButton(
                      context,
                      title: 'Levels',
                      icon: Icons.layers,
                      onTap: () {
                        SystemSound.play(SystemSoundType.click);
                        HapticFeedback.selectionClick();
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const LevelsScreen(),
                          ),
                        );
                      },
                    ),
                    
                    const SizedBox(height: 20),
                    
                    _buildMenuButton(
                      context,
                      title: 'Settings',
                      icon: Icons.settings,
                      onTap: () async {
                        SystemSound.play(SystemSoundType.click);
                        HapticFeedback.selectionClick();
                        final result = await Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SettingsScreen(),
                          ),
                        );
                        
                        // Apply settings if returned
                        if (result != null && result is Map<String, dynamic>) {
                          _applySettings(result);
                        }
                      },
                    ),
                    
                    const SizedBox(height: 20),
                    
                    _buildMenuButton(
                      context,
                      title: 'Exit',
                      icon: Icons.exit_to_app,
                      onTap: () {
                        SystemNavigator.pop();
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(
    BuildContext context, {
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF66BB6A), Color(0xFF4CAF50)],
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            onTap();
          },
          borderRadius: BorderRadius.circular(15),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 15),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class GameScreen extends StatefulWidget {
  final LevelData? levelData;
  final GameSettings? gameSettings;
  
  const GameScreen({super.key, this.levelData, this.gameSettings});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with TickerProviderStateMixin {
  static const int gridWidth = 30;
  static const int gridHeight = 20;
  
  // Game settings and level data
  late LevelData currentLevel;
  late GameSettings gameSettings;
  int gameSpeed = 8; // milliseconds - will be set based on level (much faster speed)

  // Snake body segments with smooth positions
  List<SnakeSegment> snakeSegments = [];
  List<Offset> targetPositions = [const Offset(15.0, 10.0)];
  Offset food = const Offset(5, 5);
  Offset direction = const Offset(1, 0);
  double headRotation = 0.0;
  double targetHeadRotation = 0.0;
  
  bool isGameRunning = false;
  bool isGameOver = false;
  int score = 0;
  int currentLevelNumber = 1;
  Timer? gameTimer;
  
  // Animation controllers for smooth movement
  late AnimationController _movementController;
  late AnimationController _headRotationController;
  
  // Audio players
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // Mouth animation for eating
  bool isMouthOpen = false;
  double mouthOpenAmount = 0.0;
  Timer? mouthAnimationTimer;
  
  // Smooth movement variables
  List<Offset> smoothPositions = [];
  double movementProgress = 0.0;

  @override
  void initState() {
    super.initState();
    
    // Initialize level and settings
    currentLevel = widget.levelData ?? _getDefaultLevel();
    gameSettings = widget.gameSettings ?? GameSettings();
    
    // Set default game speed first
    gameSpeed = 120;
    
    // Calculate game speed based on level and user settings
    _calculateGameSpeed();
    
    // Force landscape orientation
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    // Hide status bar for full screen experience
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    
    // Initialize animation controllers
    _movementController = AnimationController(
      duration: Duration(milliseconds: gameSpeed),
      vsync: this,
    );
    
    _headRotationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    // Initialize snake
    initializeSnake();
    generateFood();
    
    // Initialize smooth positions
    _initializeSmoothPositions();
    
    // Setup audio
    _setupAudio();
  }

  void _calculateGameSpeed() {
    // Simple speed calculation
    gameSpeed = 120; // Fixed speed for now to avoid issues
    
    // Apply user speed multiplier if available
    if (gameSettings != null) {
      double speedMultiplier = gameSettings.gameSpeed;
      gameSpeed = (120 / speedMultiplier).round().clamp(60, 200);
    }
  }

  LevelData _getDefaultLevel() {
    return LevelData(
      level: 1,
      name: 'Peaceful Garden',
      description: 'Practice Level',
      difficulty: 'Easy',
      speed: 100,
      obstacles: [
        ObstacleData(type: ObstacleType.rock, position: Offset(10, 8)),
        ObstacleData(type: ObstacleType.rock, position: Offset(20, 12)),
      ],
      requiredScore: 0,
      isUnlocked: true,
    );
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    mouthAnimationTimer?.cancel();
    _movementController.dispose();
    _headRotationController.dispose();
    _audioPlayer.dispose();
    // Reset orientation when leaving
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  void _initializeSmoothPositions() {
    smoothPositions.clear();
    for (var segment in snakeSegments) {
      smoothPositions.add(segment.position);
    }
  }

  Future<void> _setupAudio() async {
    // Audio setup for system sounds and haptic feedback
    try {
      // Initialize audio system
      // Using system sounds for better compatibility
    } catch (e) {
      // Audio setup failed, continue without sound
    }
  }

  Future<void> _playSound(String soundType) async {
    try {
      switch (soundType) {
        case 'eat':
          // Play eating sound (click with haptic feedback)
          SystemSound.play(SystemSoundType.click);
          HapticFeedback.lightImpact();
          break;
        case 'fruit':
          // Play fruit appear sound
          SystemSound.play(SystemSoundType.click);
          break;
        case 'move':
          // Very subtle movement sound (no actual sound, just haptic)
          if (Random().nextInt(50) == 0) {
            HapticFeedback.selectionClick();
          }
          break;
        case 'gameOver':
          // Game over sound
          SystemSound.play(SystemSoundType.alert);
          HapticFeedback.heavyImpact();
          break;
        case 'gameStart':
          // Game start sound
          SystemSound.play(SystemSoundType.click);
          HapticFeedback.lightImpact();
          break;
      }
    } catch (e) {
      // Sound play failed, continue silently
    }
  }

  void initializeSnake() {
    snakeSegments.clear();
    targetPositions.clear();
    
    // Create initial snake with 4 segments
    for (int i = 0; i < 4; i++) {
      bool isHead = i == 0;
      bool isTail = i == 3;
      
      snakeSegments.add(SnakeSegment(
        position: Offset(15.0 - i * 0.8, 10.0),
        targetPosition: Offset(15.0 - i * 0.8, 10.0),
        rotation: 0.0,
        scale: isHead ? 2.0 : (isTail ? 1.2 : 1.5),
        isHead: isHead,
        isTail: isTail,
        width: isHead ? 2.2 : (isTail ? 1.0 : 1.6),
        height: isHead ? 1.8 : (isTail ? 0.8 : 1.2),
      ));
      targetPositions.add(Offset(15.0 - i * 0.8, 10.0));
    }
  }

  void startGame() {
    // Recalculate speed before starting
    _calculateGameSpeed();
    
    setState(() {
      initializeSnake();
      direction = const Offset(1, 0);
      headRotation = 0.0;
      targetHeadRotation = 0.0;
      isGameRunning = true;
      isGameOver = false;
      score = 0;
    });
    generateFood();
    
    // Play game start sound
    _playSound('gameStart');
    
    gameTimer = Timer.periodic(Duration(milliseconds: gameSpeed), (timer) {
      moveSnake();
    });
  }

  void pauseGame() {
    setState(() {
      isGameRunning = false;
    });
    gameTimer?.cancel();
  }

  void resumeGame() {
    setState(() {
      isGameRunning = true;
    });
    
    gameTimer = Timer.periodic(Duration(milliseconds: gameSpeed), (timer) {
      moveSnake();
    });
  }

  void resetGame() {
    gameTimer?.cancel();
    setState(() {
      initializeSnake();
      direction = const Offset(1, 0);
      headRotation = 0.0;
      targetHeadRotation = 0.0;
      isGameRunning = false;
      isGameOver = false;
      score = 0;
      currentLevelNumber = 1;
      currentLevel = _getLevelData(1);
    });
    
    // Recalculate speed with user settings
    _calculateGameSpeed();
    
    generateFood();
  }

  void moveSnake() {
    if (!isGameRunning || snakeSegments.isEmpty) return;

    setState(() {
      // Calculate new head position with ultra-smooth movement
      Offset currentHead = snakeSegments[0].position;
      Offset newHead = Offset(
        currentHead.dx + direction.dx * 0.15, // Larger steps for faster movement
        currentHead.dy + direction.dy * 0.15,
      );

      // Check wall collision
      if (newHead.dx < 0.5 || newHead.dx >= gridWidth - 0.5 || 
          newHead.dy < 0.5 || newHead.dy >= gridHeight - 0.5) {
        gameOver();
        return;
      }

      // Check self collision
      for (int i = 3; i < snakeSegments.length; i++) { // Skip first 3 segments
        if ((newHead - snakeSegments[i].position).distance < 0.7) {
          gameOver();
          return;
        }
      }

      // Check obstacle collision
      if (ObstaclePainter.checkCollisionWithObstacles(newHead, currentLevel.obstacles)) {
        gameOver();
        return;
      }

      // Ultra-smooth head rotation
      double newRotation = atan2(direction.dy, direction.dx);
      double rotationDiff = newRotation - headRotation;
      if (rotationDiff > pi) {
        headRotation += 2 * pi;
      } else if (rotationDiff < -pi) {
        headRotation -= 2 * pi;
      }
      
      // Much smoother rotation interpolation
      headRotation = lerpDouble(headRotation, newRotation, 0.12) ?? headRotation;
      snakeSegments[0].rotation = headRotation;

      // Smooth head movement without jitter
      snakeSegments[0].targetPosition = newHead;
      
      // Update body with ultra-smooth following
      _updateUltraSmoothBody();

      // Check food collision and animate mouth
      double distanceToFood = (newHead - food).distance;
      if (distanceToFood < 1.2) {
        // Start opening mouth when approaching food
        _startMouthAnimation();
      }
      
      if (distanceToFood < 0.8) {
        // Eat the food
        _playSound('eat');
        score += 10;
        addBodySegment();
        
        // Check for level progression
        _checkLevelProgression();
        
        generateFood();
        _playSound('fruit'); // New fruit appears
        
        // Close mouth after eating
        _closeMouth();
      }
      
      // Ultra-smooth position interpolation
      for (int i = 0; i < snakeSegments.length; i++) {
        double lerpFactor = _calculateSmoothLerpFactor(i);
        
        snakeSegments[i].position = Offset(
          lerpDouble(snakeSegments[i].position.dx, snakeSegments[i].targetPosition.dx, lerpFactor) ?? snakeSegments[i].position.dx,
          lerpDouble(snakeSegments[i].position.dy, snakeSegments[i].targetPosition.dy, lerpFactor) ?? snakeSegments[i].position.dy,
        );
        
        // Very subtle oscillation for natural movement
        snakeSegments[i].oscillation = sin(DateTime.now().millisecondsSinceEpoch * 0.002 + i * 0.6) * 0.008;
      }
      
      // Play subtle movement sound occasionally
      if (Random().nextInt(20) == 0) {
        _playSound('move');
      }
    });
  }

  double _calculateSmoothLerpFactor(int segmentIndex) {
    if (segmentIndex == 0) {
      return 0.98; // Head moves very fast and smooth
    } else {
      // Body segments move progressively slower for rope-like effect
      double baseFactor = 0.92;
      double decay = segmentIndex * 0.008;
      return (baseFactor - decay).clamp(0.6, 0.92);
    }
  }

  void _updateUltraSmoothBody() {
    for (int i = 1; i < snakeSegments.length; i++) {
      Offset previousPos = snakeSegments[i - 1].position;
      Offset currentPos = snakeSegments[i].position;
      
      // Calculate smooth following distance
      Offset diff = previousPos - currentPos;
      double distance = diff.distance;
      
      // Dynamic optimal distance for rope-like behavior
      double optimalDistance = 0.5 + (i * 0.015);
      
      if (distance > optimalDistance) {
        Offset direction = diff / distance;
        
        // Ultra-smooth spring physics
        double springForce = (distance - optimalDistance) * 0.4;
        Offset springOffset = direction * springForce;
        
        snakeSegments[i].targetPosition = currentPos + springOffset;
        
        // Ultra-smooth rotation following
        double targetRotation = atan2(direction.dy, direction.dx);
        double currentRotation = snakeSegments[i].rotation;
        
        // Handle rotation wrapping
        double rotationDiff = targetRotation - currentRotation;
        if (rotationDiff > pi) {
          currentRotation += 2 * pi;
        } else if (rotationDiff < -pi) {
          currentRotation -= 2 * pi;
        }
        
        // Much smoother rotation with progressive lag
        double rotationLerpFactor = (0.08 - (i * 0.001)).clamp(0.02, 0.08);
        snakeSegments[i].rotation = lerpDouble(
          currentRotation, 
          targetRotation, 
          rotationLerpFactor
        ) ?? currentRotation;
      }
    }
  }

  void _startMouthAnimation() {
    if (!isMouthOpen) {
      setState(() {
        isMouthOpen = true;
      });
      
      // Animate mouth opening
      mouthAnimationTimer?.cancel();
      mouthAnimationTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
        setState(() {
          mouthOpenAmount = (mouthOpenAmount + 0.1).clamp(0.0, 1.0);
          if (mouthOpenAmount >= 1.0) {
            timer.cancel();
          }
        });
      });
    }
  }

  void _closeMouth() {
    mouthAnimationTimer?.cancel();
    mouthAnimationTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      setState(() {
        mouthOpenAmount = (mouthOpenAmount - 0.15).clamp(0.0, 1.0);
        if (mouthOpenAmount <= 0.0) {
          isMouthOpen = false;
          timer.cancel();
        }
      });
    });
  }

  void updateBodySegmentsWithPhysics() {
    for (int i = 1; i < snakeSegments.length; i++) {
      Offset previousPos = snakeSegments[i - 1].position;
      Offset currentPos = snakeSegments[i].position;
      
      // Calculate distance and direction to previous segment
      Offset diff = previousPos - currentPos;
      double distance = diff.distance;
      
      // Dynamic optimal distance based on segment position
      double optimalDistance = 0.8 + (i * 0.05); // Segments get slightly more spaced towards tail
      
      if (distance > optimalDistance) {
        Offset direction = diff / distance;
        
        // Apply spring physics for more natural movement
        double springForce = (distance - optimalDistance) * 0.5;
        Offset springOffset = direction * springForce;
        
        snakeSegments[i].targetPosition = currentPos + springOffset;
        
        // Smooth rotation with lag for more realistic body movement
        double targetRotation = atan2(direction.dy, direction.dx);
        double currentRotation = snakeSegments[i].rotation;
        
        // Handle rotation wrapping
        double rotationDiff = targetRotation - currentRotation;
        if (rotationDiff > pi) {
          currentRotation += 2 * pi;
        } else if (rotationDiff < -pi) {
          currentRotation -= 2 * pi;
        }
        
        // Apply rotation with lag (body follows head rotation slowly)
        double rotationLerpFactor = 0.1 - (i * 0.005); // Tail rotates slower
        snakeSegments[i].rotation = lerpDouble(
          currentRotation, 
          targetRotation, 
          rotationLerpFactor.clamp(0.02, 0.1)
        ) ?? currentRotation;
      }
      
      // Add subtle side-to-side movement for realism
      double sideMovement = sin(DateTime.now().millisecondsSinceEpoch * 0.003 + i * 0.8) * 0.03;
      Offset perpendicular = Offset(-sin(snakeSegments[i].rotation), cos(snakeSegments[i].rotation));
      snakeSegments[i].targetPosition = snakeSegments[i].targetPosition + perpendicular * sideMovement;
    }
    
    // Special handling for tail - make it more flexible
    if (snakeSegments.length > 2) {
      int tailIndex = snakeSegments.length - 1;
      
      // Add extra flexibility to tail
      double tailOscillation = sin(DateTime.now().millisecondsSinceEpoch * 0.008) * 0.1;
      Offset tailPerpendicular = Offset(-sin(snakeSegments[tailIndex].rotation), cos(snakeSegments[tailIndex].rotation));
      snakeSegments[tailIndex].targetPosition = snakeSegments[tailIndex].targetPosition + tailPerpendicular * tailOscillation;
      
      // Make tail slightly smaller
      snakeSegments[tailIndex].scale = 0.6;
    }
  }

  void addBodySegment() {
    if (snakeSegments.isNotEmpty) {
      // Update previous tail to be body segment
      if (snakeSegments.length > 1) {
        snakeSegments[snakeSegments.length - 1].isTail = false;
        snakeSegments[snakeSegments.length - 1].width = 1.0;
        snakeSegments[snakeSegments.length - 1].height = 0.8;
      }
      
      Offset lastPos = snakeSegments.last.position;
      snakeSegments.add(SnakeSegment(
        position: lastPos,
        targetPosition: lastPos,
        rotation: snakeSegments.last.rotation,
        scale: 0.7,
        isTail: true,
        width: 0.6,
        height: 0.5,
      ));
    }
  }

  void generateFood() {
    Random random = Random();
    Offset newFood;
    bool validPosition = false;
    
    do {
      newFood = Offset(
        random.nextInt(gridWidth - 2).toDouble() + 1,
        random.nextInt(gridHeight - 2).toDouble() + 1,
      );
      
      validPosition = true;
      // Check if food is too close to any snake segment
      for (var segment in snakeSegments) {
        if ((newFood - segment.position).distance < 2.0) {
          validPosition = false;
          break;
        }
      }
      
      // Check if food is too close to any obstacle
      if (validPosition) {
        for (final obstacle in currentLevel.obstacles) {
          if ((newFood - obstacle.position).distance < 1.5) {
            validPosition = false;
            break;
          }
        }
      }
    } while (!validPosition);
    
    setState(() {
      food = newFood;
    });
    
    // Play fruit appear sound
    _playSound('fruit');
  }

  void _checkLevelProgression() {
    // Define level thresholds and corresponding levels
    List<int> levelThresholds = [100, 200, 350, 500, 700, 950, 1250, 1600, 2000, 2500];
    
    for (int i = 0; i < levelThresholds.length; i++) {
      if (score >= levelThresholds[i] && currentLevelNumber <= i + 1) {
        // Progress to next level
        _progressToNextLevel(i + 2); // Level starts from 1, so +2
        break;
      }
    }
  }

  void _progressToNextLevel(int newLevelNumber) {
    // Get the new level data
    LevelData newLevel = _getLevelData(newLevelNumber);
    
    setState(() {
      currentLevel = newLevel;
      currentLevelNumber = newLevelNumber;
    });
    
    // Recalculate speed with user settings
    _calculateGameSpeed();
    
    // Update game timer with new speed if game is running
    if (isGameRunning) {
      gameTimer?.cancel();
      gameTimer = Timer.periodic(Duration(milliseconds: gameSpeed), (timer) {
        moveSnake();
      });
    }
    
    // Show level up notification
    _showLevelUpNotification(newLevelNumber);
    
    print('Progressed to level $newLevelNumber with speed $gameSpeed ms');
  }

  LevelData _getLevelData(int level) {
    // Define levels with increasing difficulty
    switch (level) {
      case 1:
        return LevelData(
          level: 1,
          name: 'Peaceful Garden',
          description: 'Practice Level',
          difficulty: 'Easy',
          speed: 100,
          obstacles: [
            ObstacleData(type: ObstacleType.rock, position: Offset(10, 8)),
            ObstacleData(type: ObstacleType.rock, position: Offset(20, 12)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(15, 6)),
          ],
          requiredScore: 0,
          isUnlocked: true,
        );
      case 2:
        return LevelData(
          level: 2,
          name: 'Rocky Path',
          description: 'First Challenge',
          difficulty: 'Easy',
          speed: 90,
          obstacles: [
            ObstacleData(type: ObstacleType.rock, position: Offset(8, 5)),
            ObstacleData(type: ObstacleType.rock, position: Offset(12, 5)),
            ObstacleData(type: ObstacleType.rock, position: Offset(16, 5)),
            ObstacleData(type: ObstacleType.rock, position: Offset(20, 8)),
            ObstacleData(type: ObstacleType.rock, position: Offset(15, 12)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(10, 10)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(18, 14)),
          ],
          requiredScore: 100,
          isUnlocked: true,
        );
      case 3:
        return LevelData(
          level: 3,
          name: 'Pillar Forest',
          description: 'Navigate the Pillars',
          difficulty: 'Medium',
          speed: 85,
          obstacles: [
            ObstacleData(type: ObstacleType.pillar, position: Offset(6, 4)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(10, 4)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(14, 4)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(18, 4)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(22, 4)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(8, 8)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(12, 8)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(16, 8)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(20, 8)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(6, 12)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(10, 12)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(14, 12)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(18, 12)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(22, 12)),
            ObstacleData(type: ObstacleType.rock, position: Offset(4, 8)),
            ObstacleData(type: ObstacleType.rock, position: Offset(24, 8)),
          ],
          requiredScore: 200,
          isUnlocked: true,
        );
      case 4:
        return LevelData(
          level: 4,
          name: 'Tree Field',
          description: 'Dense Forest',
          difficulty: 'Medium',
          speed: 80,
          obstacles: [
            ObstacleData(type: ObstacleType.tree, position: Offset(5, 3)),
            ObstacleData(type: ObstacleType.tree, position: Offset(9, 3)),
            ObstacleData(type: ObstacleType.tree, position: Offset(13, 3)),
            ObstacleData(type: ObstacleType.tree, position: Offset(17, 3)),
            ObstacleData(type: ObstacleType.tree, position: Offset(21, 3)),
            ObstacleData(type: ObstacleType.tree, position: Offset(25, 3)),
            ObstacleData(type: ObstacleType.tree, position: Offset(3, 7)),
            ObstacleData(type: ObstacleType.tree, position: Offset(7, 7)),
            ObstacleData(type: ObstacleType.tree, position: Offset(11, 7)),
            ObstacleData(type: ObstacleType.tree, position: Offset(15, 7)),
            ObstacleData(type: ObstacleType.tree, position: Offset(19, 7)),
            ObstacleData(type: ObstacleType.tree, position: Offset(23, 7)),
            ObstacleData(type: ObstacleType.tree, position: Offset(27, 7)),
            ObstacleData(type: ObstacleType.tree, position: Offset(5, 11)),
            ObstacleData(type: ObstacleType.tree, position: Offset(9, 11)),
            ObstacleData(type: ObstacleType.tree, position: Offset(13, 11)),
            ObstacleData(type: ObstacleType.tree, position: Offset(17, 11)),
            ObstacleData(type: ObstacleType.tree, position: Offset(21, 11)),
            ObstacleData(type: ObstacleType.tree, position: Offset(25, 11)),
            ObstacleData(type: ObstacleType.tree, position: Offset(3, 15)),
            ObstacleData(type: ObstacleType.tree, position: Offset(7, 15)),
            ObstacleData(type: ObstacleType.tree, position: Offset(11, 15)),
            ObstacleData(type: ObstacleType.tree, position: Offset(15, 15)),
            ObstacleData(type: ObstacleType.tree, position: Offset(19, 15)),
            ObstacleData(type: ObstacleType.tree, position: Offset(23, 15)),
            ObstacleData(type: ObstacleType.tree, position: Offset(27, 15)),
          ],
          requiredScore: 350,
          isUnlocked: true,
        );
      case 5:
        return LevelData(
          level: 5,
          name: 'Stone Maze',
          description: 'Navigate the Maze',
          difficulty: 'Hard',
          speed: 75,
          obstacles: [
            // Create a complex maze-like pattern
            ObstacleData(type: ObstacleType.rock, position: Offset(4, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(5, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(6, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(7, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(10, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(11, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(12, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(13, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(16, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(17, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(18, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(19, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(22, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(23, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(24, 2)),
            ObstacleData(type: ObstacleType.rock, position: Offset(25, 2)),
            // Vertical walls
            ObstacleData(type: ObstacleType.rock, position: Offset(4, 5)),
            ObstacleData(type: ObstacleType.rock, position: Offset(7, 5)),
            ObstacleData(type: ObstacleType.rock, position: Offset(10, 5)),
            ObstacleData(type: ObstacleType.rock, position: Offset(13, 5)),
            ObstacleData(type: ObstacleType.rock, position: Offset(16, 5)),
            ObstacleData(type: ObstacleType.rock, position: Offset(19, 5)),
            ObstacleData(type: ObstacleType.rock, position: Offset(22, 5)),
            ObstacleData(type: ObstacleType.rock, position: Offset(25, 5)),
            // Middle horizontal barriers
            ObstacleData(type: ObstacleType.rock, position: Offset(5, 8)),
            ObstacleData(type: ObstacleType.rock, position: Offset(6, 8)),
            ObstacleData(type: ObstacleType.rock, position: Offset(11, 8)),
            ObstacleData(type: ObstacleType.rock, position: Offset(12, 8)),
            ObstacleData(type: ObstacleType.rock, position: Offset(17, 8)),
            ObstacleData(type: ObstacleType.rock, position: Offset(18, 8)),
            ObstacleData(type: ObstacleType.rock, position: Offset(23, 8)),
            ObstacleData(type: ObstacleType.rock, position: Offset(24, 8)),
            // Lower section
            ObstacleData(type: ObstacleType.rock, position: Offset(4, 11)),
            ObstacleData(type: ObstacleType.rock, position: Offset(7, 11)),
            ObstacleData(type: ObstacleType.rock, position: Offset(10, 11)),
            ObstacleData(type: ObstacleType.rock, position: Offset(13, 11)),
            ObstacleData(type: ObstacleType.rock, position: Offset(16, 11)),
            ObstacleData(type: ObstacleType.rock, position: Offset(19, 11)),
            ObstacleData(type: ObstacleType.rock, position: Offset(22, 11)),
            ObstacleData(type: ObstacleType.rock, position: Offset(25, 11)),
            // Bottom barriers
            ObstacleData(type: ObstacleType.rock, position: Offset(4, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(5, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(6, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(7, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(10, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(11, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(12, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(13, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(16, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(17, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(18, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(19, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(22, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(23, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(24, 14)),
            ObstacleData(type: ObstacleType.rock, position: Offset(25, 14)),
            // Add some pillars for variety
            ObstacleData(type: ObstacleType.pillar, position: Offset(8, 6)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(14, 6)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(20, 6)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(8, 12)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(14, 12)),
            ObstacleData(type: ObstacleType.pillar, position: Offset(20, 12)),
          ],
          requiredScore: 500,
          isUnlocked: true,
        );
      default:
        // For levels beyond 5, create increasingly difficult patterns
        int obstacleCount = (level * 6).clamp(15, 60);
        List<ObstacleData> obstacles = [];
        Random random = Random(level); // Use level as seed for consistent patterns
        
        for (int i = 0; i < obstacleCount; i++) {
          obstacles.add(ObstacleData(
            type: ObstacleType.values[random.nextInt(ObstacleType.values.length)],
            position: Offset(
              random.nextInt(gridWidth - 4).toDouble() + 2,
              random.nextInt(gridHeight - 4).toDouble() + 2,
            ),
          ));
        }
        
        return LevelData(
          level: level,
          name: 'Challenge Level $level',
          description: 'Ultimate Test',
          difficulty: 'Extreme',
          speed: (120 - level * 5).clamp(50, 100), // Much faster speeds
          obstacles: obstacles,
          requiredScore: level * 150,
          isUnlocked: true,
        );
    }
  }

  void _showLevelUpNotification(int level) {
    // Show a brief notification about level up
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Level Up! Welcome to ${currentLevel.name}',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void gameOver() {
    gameTimer?.cancel();
    setState(() {
      isGameRunning = false;
      isGameOver = true;
    });
    
    // Play game over sound
    _playSound('gameOver');
    
    // Show game over screen
    _showGameOverScreen();
  }

  void _showGameOverScreen() async {
    // Check if level is complete
    bool isLevelComplete = currentLevel.requiredScore > 0 && score >= currentLevel.requiredScore;
    
    if (!mounted) return;
    
    final result = await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => GameOverScreen(
        score: score,
        levelData: currentLevel,
        isLevelComplete: isLevelComplete,
      ),
    );
    
    if (!mounted) return;
    
    switch (result) {
      case 'retry':
        resetGame();
        break;
      case 'home':
        Navigator.popUntil(context, (route) => route.isFirst);
        break;
      case 'next_level':
        // Go to next level
        _goToNextLevel();
        break;
    }
  }

  void _goToNextLevel() {
    // This would typically load the next level
    // For now, just restart the current level
    resetGame();
  }

  void handlePanUpdate(DragUpdateDetails details) {
    if (!isGameRunning) return;
    
    // Get the delta (change in position) with sensitivity
    double dx = details.delta.dx;
    double dy = details.delta.dy;
    
    // Require minimum movement to prevent jittery controls
    double minMovement = 3.0;
    if (dx.abs() < minMovement && dy.abs() < minMovement) return;
    
    // Calculate the angle of movement for more precise control
    double angle = atan2(dy, dx);
    
    // Convert angle to direction with 8-directional movement
    Offset newDirection;
    
    if (angle >= -pi/8 && angle < pi/8) {
      newDirection = const Offset(1, 0); // Right
    } else if (angle >= pi/8 && angle < 3*pi/8) {
      newDirection = const Offset(0.707, 0.707); // Down-Right
    } else if (angle >= 3*pi/8 && angle < 5*pi/8) {
      newDirection = const Offset(0, 1); // Down
    } else if (angle >= 5*pi/8 && angle < 7*pi/8) {
      newDirection = const Offset(-0.707, 0.707); // Down-Left
    } else if (angle >= 7*pi/8 || angle < -7*pi/8) {
      newDirection = const Offset(-1, 0); // Left
    } else if (angle >= -7*pi/8 && angle < -5*pi/8) {
      newDirection = const Offset(-0.707, -0.707); // Up-Left
    } else if (angle >= -5*pi/8 && angle < -3*pi/8) {
      newDirection = const Offset(0, -1); // Up
    } else {
      newDirection = const Offset(0.707, -0.707); // Up-Right
    }
    
    // Normalize diagonal directions for consistent speed
    if (newDirection.dx != 0 && newDirection.dy != 0) {
      double length = sqrt(newDirection.dx * newDirection.dx + newDirection.dy * newDirection.dy);
      newDirection = Offset(newDirection.dx / length, newDirection.dy / length);
    }
    
    changeDirection(newDirection);
  }

  void changeDirection(Offset newDirection) {
    if (!isGameRunning) return;
    
    // Prevent reverse direction (opposite of current direction)
    if (direction.dx == -newDirection.dx && direction.dy == -newDirection.dy) {
      return;
    }
    
    // Smooth direction change
    setState(() {
      direction = newDirection;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            // Full screen game board
            GestureDetector(
              onPanUpdate: handlePanUpdate,
              child: Container(
                width: double.infinity,
                height: double.infinity,
                child: CustomPaint(
                  painter: RealisticSnakePainter(snakeSegments, food, gridWidth, gridHeight, isMouthOpen, mouthOpenAmount, currentLevel.obstacles),
                  size: Size.infinite,
                ),
              ),
            ),
            
            // Modern top score display
            Positioned(
              top: 16,
              left: 16,
              right: 16,
              child: Container(
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.black.withValues(alpha: 0.15),
                      Colors.black.withValues(alpha: 0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(
                    color: Colors.green.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildModernScoreItem('SCORE', score.toString(), Colors.green, Icons.stars),
                    ),
                    Container(
                      width: 1,
                      height: 30,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.green.withValues(alpha: 0.3),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: _buildModernScoreItem('LENGTH', snakeSegments.length.toString(), Colors.blue, Icons.straighten),
                    ),
                    Container(
                      width: 1,
                      height: 30,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.orange.withValues(alpha: 0.3),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: _buildModernScoreItem('LEVEL', currentLevelNumber.toString(), Colors.orange, Icons.layers),
                    ),
                  ],
                ),
              ),
            ),
            
            // Modern control buttons - top left
            Positioned(
              top: 90,
              left: 16,
              child: Column(
                children: [
                  _buildModernControlButton(
                    icon: isGameRunning ? Icons.pause_circle_filled : (isGameOver ? Icons.refresh : Icons.play_circle_filled),
                    onPressed: isGameRunning ? pauseGame : (isGameOver ? resetGame : startGame),
                    color: isGameRunning ? Colors.orange : (isGameOver ? Colors.red : Colors.green),
                    size: 56,
                  ),
                  
                  if (!isGameRunning && !isGameOver && snakeSegments.length > 1) ...[
                    const SizedBox(height: 12),
                    _buildModernControlButton(
                      icon: Icons.play_circle_filled,
                      onPressed: resumeGame,
                      color: Colors.blue,
                      size: 48,
                    ),
                  ],
                  
                  const SizedBox(height: 12),
                  _buildModernControlButton(
                    icon: Icons.home_filled,
                    onPressed: () => Navigator.pop(context),
                    color: Colors.grey[600]!,
                    size: 48,
                  ),
                ],
              ),
            ),
            

          ],
        ),
      ),
    );
  }

  void _changeDirection(Offset newDirection) {
    if (!isGameRunning) return;
    
    // Prevent reversing into itself
    if (direction.dx != 0 && newDirection.dx != 0) return;
    if (direction.dy != 0 && newDirection.dy != 0) return;
    
    setState(() {
      direction = newDirection;
      
      // Update target head rotation for smooth turning
      if (direction.dx == 1) {
        targetHeadRotation = 0; // Right
      } else if (direction.dx == -1) {
        targetHeadRotation = pi; // Left
      } else if (direction.dy == 1) {
        targetHeadRotation = pi / 2; // Down
      } else if (direction.dy == -1) {
        targetHeadRotation = -pi / 2; // Up
      }
    });
  }


  
  Widget _buildModernScoreItem(String label, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(width: 8),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildModernControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color color,
    required double size,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.9),
            color.withValues(alpha: 0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(size / 2),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            onPressed();
          },
          borderRadius: BorderRadius.circular(size / 2),
          child: Icon(
            icon,
            color: Colors.white,
            size: size * 0.5,
          ),
        ),
      ),
    );
  }
}

// Snake segment class for realistic movement
class SnakeSegment {
  Offset position;
  Offset targetPosition;
  double rotation;
  double scale;
  double oscillation; // For realistic body movement
  bool isHead;
  bool isTail;
  double width;
  double height;

  SnakeSegment({
    required this.position,
    required this.targetPosition,
    required this.rotation,
    required this.scale,
    this.oscillation = 0.0,
    this.isHead = false,
    this.isTail = false,
    this.width = 1.0,
    this.height = 0.8,
  });
}

// Ultra Realistic Snake Painter
class RealisticSnakePainter extends CustomPainter {
  final List<SnakeSegment> snakeSegments;
  final Offset food;
  final int gridWidth;
  final int gridHeight;
  final bool isMouthOpen;
  final double mouthOpenAmount;
  final List<ObstacleData> obstacles;

  RealisticSnakePainter(
    this.snakeSegments, 
    this.food, 
    this.gridWidth, 
    this.gridHeight,
    this.isMouthOpen,
    this.mouthOpenAmount,
    this.obstacles,
  );

  @override
  void paint(Canvas canvas, Size size) {
    final double cellWidth = size.width / gridWidth;
    final double cellHeight = size.height / gridHeight;

    // Draw subtle grid for reference
    _drawGrid(canvas, size, cellWidth, cellHeight);
    
    // Draw obstacles first (behind everything)
    ObstaclePainter.drawObstacles(canvas, obstacles, cellWidth, cellHeight);
    
    // Draw pulsating food (behind snake)
    _drawPulsatingFood(canvas, cellWidth, cellHeight);
    
    // Draw realistic snake with rectangular body
    _drawUltraRealisticSnake(canvas, cellWidth, cellHeight);
  }

  void _drawGrid(Canvas canvas, Size size, double cellWidth, double cellHeight) {
    // Draw grass background
    _drawGrassBackground(canvas, size, cellWidth, cellHeight);
    
    // Draw subtle grid lines (optional)
    final gridPaint = Paint()
      ..color = Colors.green.withValues(alpha: 0.05)
      ..strokeWidth = 0.3;

    // Draw very subtle grid for reference
    for (int i = 0; i <= gridWidth; i += 5) {
      canvas.drawLine(
        Offset(i * cellWidth, 0),
        Offset(i * cellWidth, size.height),
        gridPaint,
      );
    }

    for (int i = 0; i <= gridHeight; i += 5) {
      canvas.drawLine(
        Offset(0, i * cellHeight),
        Offset(size.width, i * cellHeight),
        gridPaint,
      );
    }
  }

  void _drawGrassBackground(Canvas canvas, Size size, double cellWidth, double cellHeight) {
    // Create grass gradient background
    final grassGradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        const Color(0xFF4CAF50), // Light green
        const Color(0xFF388E3C), // Medium green
        const Color(0xFF2E7D32), // Dark green
      ],
      stops: const [0.0, 0.5, 1.0],
    );

    final backgroundPaint = Paint()
      ..shader = grassGradient.createShader(
        Rect.fromLTWH(0, 0, size.width, size.height),
      );

    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      backgroundPaint,
    );

    // Add grass texture
    _drawGrassTexture(canvas, size, cellWidth, cellHeight);
  }

  void _drawGrassTexture(Canvas canvas, Size size, double cellWidth, double cellHeight) {
    final grassPaint = Paint()
      ..color = Colors.green[600]!.withValues(alpha: 0.3)
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;

    final lightGrassPaint = Paint()
      ..color = Colors.green[300]!.withValues(alpha: 0.2)
      ..strokeWidth = 1.0
      ..strokeCap = StrokeCap.round;

    // Draw random grass blades
    final random = Random(42); // Fixed seed for consistent pattern
    
    for (int i = 0; i < (size.width * size.height / 1000).round(); i++) {
      double x = random.nextDouble() * size.width;
      double y = random.nextDouble() * size.height;
      double height = random.nextDouble() * 8 + 3;
      double angle = (random.nextDouble() - 0.5) * 0.5;
      
      canvas.save();
      canvas.translate(x, y);
      canvas.rotate(angle);
      
      // Draw grass blade
      canvas.drawLine(
        Offset.zero,
        Offset(0, -height),
        random.nextBool() ? grassPaint : lightGrassPaint,
      );
      
      canvas.restore();
    }

    // Add some flower spots
    _drawFlowerSpots(canvas, size, cellWidth, cellHeight);
  }

  void _drawFlowerSpots(Canvas canvas, Size size, double cellWidth, double cellHeight) {
    final flowerPaint = Paint()..color = Colors.yellow[300]!.withValues(alpha: 0.4);
    final flowerCenterPaint = Paint()..color = Colors.orange[300]!.withValues(alpha: 0.6);

    final random = Random(123); // Fixed seed
    
    for (int i = 0; i < 15; i++) {
      double x = random.nextDouble() * size.width;
      double y = random.nextDouble() * size.height;
      double flowerSize = random.nextDouble() * 4 + 2;
      
      // Draw small flower
      canvas.drawCircle(Offset(x, y), flowerSize, flowerPaint);
      canvas.drawCircle(Offset(x, y), flowerSize * 0.4, flowerCenterPaint);
    }
  }

  void _drawUltraRealisticSnake(Canvas canvas, double cellWidth, double cellHeight) {
    if (snakeSegments.isEmpty) return;

    // Draw connected snake body as one continuous shape
    _drawConnectedSnakeBody(canvas, cellWidth, cellHeight);
    
    // Draw the head separately on top
    _drawUltraRealisticHead(canvas, cellWidth, cellHeight);
  }

  void _drawConnectedSnakeBody(Canvas canvas, double cellWidth, double cellHeight) {
    if (snakeSegments.length < 2) return;

    // Create smooth curved path through all segments (like a rope)
    List<Offset> points = [];
    for (var segment in snakeSegments) {
      points.add(Offset(
        segment.position.dx * cellWidth + segment.oscillation * cellWidth,
        segment.position.dy * cellHeight + segment.oscillation * cellHeight,
      ));
    }

    // Draw body segments with tapering thickness (thicker at head, thinner at tail)
    _drawTaperingSnakeBody(canvas, points, cellWidth);
    
    // Draw scales pattern along the rope
    _drawRopeScales(canvas, points, cellWidth);
  }

  Path _createSmoothRopePath(List<Offset> points) {
    final path = Path();
    if (points.length < 2) return path;

    path.moveTo(points[0].dx, points[0].dy);

    // Use cubic bezier curves for ultra-smooth rope-like movement
    for (int i = 1; i < points.length - 1; i++) {
      Offset current = points[i];
      Offset next = points[i + 1];
      Offset prev = points[i - 1];
      
      // Calculate smooth control points
      Offset controlPoint1 = Offset(
        current.dx + (current.dx - prev.dx) * 0.3,
        current.dy + (current.dy - prev.dy) * 0.3,
      );
      Offset controlPoint2 = Offset(
        current.dx + (next.dx - current.dx) * 0.3,
        current.dy + (next.dy - current.dy) * 0.3,
      );
      
      if (i == 1) {
        path.quadraticBezierTo(current.dx, current.dy, controlPoint2.dx, controlPoint2.dy);
      } else {
        path.cubicTo(controlPoint1.dx, controlPoint1.dy, current.dx, current.dy, controlPoint2.dx, controlPoint2.dy);
      }
    }
    
    // Add final point
    if (points.length > 1) {
      path.lineTo(points.last.dx, points.last.dy);
    }

    return path;
  }

  void _drawTaperingSnakeBody(Canvas canvas, List<Offset> points, double cellWidth) {
    if (points.length < 2) return;

    // Create realistic snake body gradient
    final bodyGradient = LinearGradient(
      colors: [
        const Color(0xFF66BB6A), // Light green
        const Color(0xFF4CAF50), // Medium green  
        const Color(0xFF2E7D32), // Dark green
        const Color(0xFF1B5E20), // Very dark green
      ],
      stops: const [0.0, 0.3, 0.7, 1.0],
    );

    // Draw each segment with decreasing thickness towards the tail
    for (int i = 0; i < points.length - 1; i++) {
      double progress = i / (points.length - 1);
      
      // Calculate thickness: thick at head (progress = 0), thin at tail (progress = 1)
      double thickness = cellWidth * (1.4 - (progress * 0.8)); // From 1.4 to 0.6
      
      // Draw shadow first
      final shadowPaint = Paint()
        ..color = Colors.black.withValues(alpha: 0.3)
        ..strokeWidth = thickness + 4
        ..strokeCap = StrokeCap.round
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);

      canvas.save();
      canvas.translate(4, 4);
      canvas.drawLine(points[i], points[i + 1], shadowPaint);
      canvas.restore();

      // Draw main body segment
      final bodyPaint = Paint()
        ..shader = bodyGradient.createShader(
          Rect.fromPoints(points[i], points[i + 1]),
        )
        ..strokeWidth = thickness
        ..strokeCap = StrokeCap.round;

      canvas.drawLine(points[i], points[i + 1], bodyPaint);

      // Draw highlight for 3D effect
      final highlightPaint = Paint()
        ..color = Colors.white.withValues(alpha: 0.3)
        ..strokeWidth = thickness * 0.3
        ..strokeCap = StrokeCap.round;

      canvas.drawLine(points[i], points[i + 1], highlightPaint);

      // Draw darker outline for definition
      final outlinePaint = Paint()
        ..color = const Color(0xFF1B5E20)
        ..strokeWidth = thickness + 2
        ..strokeCap = StrokeCap.round
        ..style = PaintingStyle.stroke;

      canvas.drawLine(points[i], points[i + 1], outlinePaint);
    }
  }

  void _drawRopeBodyShadow(Canvas canvas, Path ropePath, double cellWidth) {
    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.3)
      ..strokeWidth = cellWidth * 1.4
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8)
      ..style = PaintingStyle.stroke;

    canvas.save();
    canvas.translate(6, 6);
    canvas.drawPath(ropePath, shadowPaint);
    canvas.restore();
  }

  void _drawRopeBody(Canvas canvas, Path ropePath, double cellWidth) {
    // Create realistic snake body gradient
    final bodyGradient = LinearGradient(
      colors: [
        const Color(0xFF66BB6A), // Light green
        const Color(0xFF4CAF50), // Medium green  
        const Color(0xFF2E7D32), // Dark green
        const Color(0xFF1B5E20), // Very dark green
      ],
      stops: const [0.0, 0.3, 0.7, 1.0],
    );

    // Draw main rope body with gradient
    final bodyPaint = Paint()
      ..shader = bodyGradient.createShader(
        Rect.fromLTWH(0, 0, 800, 600),
      )
      ..strokeWidth = cellWidth * 1.2
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    canvas.drawPath(ropePath, bodyPaint);

    // Draw highlight along the rope for 3D effect
    final highlightPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.25)
      ..strokeWidth = cellWidth * 0.3
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    canvas.drawPath(ropePath, highlightPaint);

    // Draw darker outline for definition
    final outlinePaint = Paint()
      ..color = const Color(0xFF1B5E20)
      ..strokeWidth = cellWidth * 1.3
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    canvas.drawPath(ropePath, outlinePaint);
  }



  void _drawRopeScales(Canvas canvas, List<Offset> points, double cellWidth) {
    final scalePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.08)
      ..strokeWidth = 1.2;

    // Draw subtle scale pattern along the rope
    for (int i = 1; i < points.length - 1; i += 2) {
      Offset current = points[i];
      
      // Calculate direction for perpendicular scales
      Offset prev = i > 0 ? points[i - 1] : current;
      Offset next = i < points.length - 1 ? points[i + 1] : current;
      Offset direction = (next - prev).normalize();
      Offset perpendicular = Offset(-direction.dy, direction.dx);
      
      // Draw cross-hatched scales
      double scaleSize = cellWidth * 0.2;
      
      canvas.drawLine(
        current + perpendicular * scaleSize,
        current - perpendicular * scaleSize,
        scalePaint,
      );
      
      // Add diagonal scales for texture
      if (i % 4 == 1) {
        Offset diagonal1 = perpendicular * 0.7 + direction * 0.3;
        Offset diagonal2 = perpendicular * 0.7 - direction * 0.3;
        
        canvas.drawLine(
          current + diagonal1 * scaleSize,
          current - diagonal1 * scaleSize,
          scalePaint,
        );
        canvas.drawLine(
          current + diagonal2 * scaleSize,
          current - diagonal2 * scaleSize,
          scalePaint,
        );
      }
    }
  }

  void _drawUltraRealisticHead(Canvas canvas, double cellWidth, double cellHeight) {
    if (snakeSegments.isEmpty) return;
    
    final headSegment = snakeSegments[0];
    final headCenter = Offset(
      headSegment.position.dx * cellWidth,
      headSegment.position.dy * cellHeight,
    );

    canvas.save();
    canvas.translate(headCenter.dx, headCenter.dy);
    canvas.rotate(headSegment.rotation);

    double headWidth = cellWidth * 1.8;
    double headHeight = cellHeight * 1.6;

    // Draw head shadow
    _drawHeadShadow(canvas, headWidth, headHeight);
    
    // Draw main head shape
    _drawMainHeadShape(canvas, headWidth, headHeight);
    
    // Draw head details
    _drawHeadDetails(canvas, headWidth, headHeight);

    canvas.restore();
  }

  void _drawHeadShadow(Canvas canvas, double width, double height) {
    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.5)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    final shadowPath = _createRealisticHeadPath(width, height);
    canvas.translate(6, 6);
    canvas.drawPath(shadowPath, shadowPaint);
    canvas.translate(-6, -6);
  }

  void _drawMainHeadShape(Canvas canvas, double width, double height) {
    // Create realistic head gradient
    final headGradient = RadialGradient(
      center: const Alignment(-0.3, -0.4),
      radius: 1.2,
      colors: [
        const Color(0xFF66BB6A), // Light green
        const Color(0xFF4CAF50), // Medium green
        const Color(0xFF2E7D32), // Dark green
        const Color(0xFF1B5E20), // Very dark green
      ],
      stops: const [0.0, 0.3, 0.7, 1.0],
    );

    final headPaint = Paint()
      ..shader = headGradient.createShader(
        Rect.fromCenter(center: Offset.zero, width: width, height: height),
      );

    final headPath = _createRealisticHeadPath(width, height);
    canvas.drawPath(headPath, headPaint);

    // Add head outline for definition
    final outlinePaint = Paint()
      ..color = const Color(0xFF1B5E20)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    canvas.drawPath(headPath, outlinePaint);
  }

  Path _createRealisticHeadPath(double width, double height) {
    final headPath = Path();
    
    // Create a more realistic snake head shape
    // Start from the neck (back of head)
    headPath.moveTo(-width * 0.3, -height * 0.4);
    
    // Top of head - curved and pointed
    headPath.quadraticBezierTo(-width * 0.1, -height * 0.6, width * 0.2, -height * 0.5);
    headPath.quadraticBezierTo(width * 0.4, -height * 0.4, width * 0.5, -height * 0.2);
    
    // Snout area - pointed
    headPath.quadraticBezierTo(width * 0.6, 0, width * 0.5, height * 0.2);
    
    // Bottom of head
    headPath.quadraticBezierTo(width * 0.4, height * 0.4, width * 0.2, height * 0.5);
    headPath.quadraticBezierTo(-width * 0.1, height * 0.6, -width * 0.3, height * 0.4);
    
    // Back to neck
    headPath.quadraticBezierTo(-width * 0.4, 0, -width * 0.3, -height * 0.4);
    
    headPath.close();
    return headPath;
  }

  void _drawHeadDetails(Canvas canvas, double width, double height) {
    // Draw realistic eyes
    _drawUltraRealisticEyes(canvas, width, height);
    
    // Draw nostrils
    _drawRealisticNostrils(canvas, width, height);
    
    // Draw mouth and tongue
    _drawRealisticMouth(canvas, width, height);
    
    // Draw head scales and texture
    _drawHeadTexture(canvas, width, height);
    
    // Add highlights for 3D effect
    _drawHeadHighlights(canvas, width, height);
  }



  void _drawHeadTexture(Canvas canvas, double width, double height) {
    final scalePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.12)
      ..strokeWidth = 0.8;

    // Draw realistic head scale pattern
    for (int i = 0; i < 4; i++) {
      for (int j = 0; j < 3; j++) {
        double x = (i - 1.5) * width * 0.15;
        double y = (j - 1) * height * 0.25;
        
        // Draw hexagonal scale pattern
        _drawHexagonalScale(canvas, Offset(x, y), width * 0.08, scalePaint);
      }
    }
    
    // Add some larger scales on top of head
    final largeScalePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.08)
      ..strokeWidth = 1.2;
    
    _drawHexagonalScale(canvas, Offset(0, -height * 0.2), width * 0.12, largeScalePaint);
    _drawHexagonalScale(canvas, Offset(-width * 0.1, 0), width * 0.1, largeScalePaint);
    _drawHexagonalScale(canvas, Offset(width * 0.1, 0), width * 0.1, largeScalePaint);
  }

  void _drawHexagonalScale(Canvas canvas, Offset center, double size, Paint paint) {
    final scalePath = Path();
    for (int i = 0; i < 6; i++) {
      double angle = (i * pi) / 3;
      double x = center.dx + cos(angle) * size;
      double y = center.dy + sin(angle) * size;
      
      if (i == 0) {
        scalePath.moveTo(x, y);
      } else {
        scalePath.lineTo(x, y);
      }
    }
    scalePath.close();
    
    canvas.drawPath(scalePath, paint);
  }

  void _drawHeadHighlights(Canvas canvas, double width, double height) {
    // Main highlight on top of head
    final highlightGradient = RadialGradient(
      colors: [
        Colors.white.withValues(alpha: 0.3),
        Colors.white.withValues(alpha: 0.1),
        Colors.transparent,
      ],
    );
    
    final highlightPaint = Paint()
      ..shader = highlightGradient.createShader(
        Rect.fromCenter(
          center: Offset(-width * 0.1, -height * 0.3),
          width: width * 0.4,
          height: height * 0.3,
        ),
      );
    
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(-width * 0.1, -height * 0.3),
        width: width * 0.4,
        height: height * 0.3,
      ),
      highlightPaint,
    );
    
    // Side highlights
    final sideHighlightPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.15);
    
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(width * 0.2, -height * 0.4),
        width: width * 0.15,
        height: height * 0.1,
      ),
      sideHighlightPaint,
    );
    
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(width * 0.2, height * 0.4),
        width: width * 0.15,
        height: height * 0.1,
      ),
      sideHighlightPaint,
    );
  }

  void _drawUltraRealisticEyes(Canvas canvas, double width, double height) {
    final time = DateTime.now().millisecondsSinceEpoch;
    
    // Eye positions - more forward on the head
    final leftEyePos = Offset(width * 0.1, -height * 0.3);
    final rightEyePos = Offset(width * 0.1, height * 0.3);
    
    // Eye size with blinking animation
    double eyeWidth = width * 0.15;
    double eyeHeight = height * 0.2;
    if (time % 4000 < 200) { // Blink every 4 seconds
      eyeHeight *= 0.1;
    }
    
    // Draw eye sockets with depth
    final socketGradient = RadialGradient(
      colors: [
        const Color(0xFF0D2818),
        const Color(0xFF1B5E20),
      ],
    );
    
    final socketPaint = Paint()
      ..shader = socketGradient.createShader(
        Rect.fromCenter(center: leftEyePos, width: eyeWidth * 1.4, height: eyeHeight * 1.4),
      );
    
    canvas.drawOval(
      Rect.fromCenter(center: leftEyePos, width: eyeWidth * 1.4, height: eyeHeight * 1.4),
      socketPaint,
    );
    canvas.drawOval(
      Rect.fromCenter(center: rightEyePos, width: eyeWidth * 1.4, height: eyeHeight * 1.4),
      socketPaint,
    );
    
    // Draw eye iris with gradient
    final irisGradient = RadialGradient(
      colors: [
        const Color(0xFFFFD700), // Gold center
        const Color(0xFFFF8F00), // Orange
        const Color(0xFFE65100), // Dark orange
      ],
    );
    
    final irisPaint = Paint()
      ..shader = irisGradient.createShader(
        Rect.fromCenter(center: leftEyePos, width: eyeWidth, height: eyeHeight),
      );
    
    canvas.drawOval(
      Rect.fromCenter(center: leftEyePos, width: eyeWidth, height: eyeHeight),
      irisPaint,
    );
    canvas.drawOval(
      Rect.fromCenter(center: rightEyePos, width: eyeWidth, height: eyeHeight),
      irisPaint,
    );
    
    // Draw pupils (vertical slits) - more realistic
    final pupilPaint = Paint()..color = Colors.black;
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(center: leftEyePos, width: width * 0.015, height: eyeHeight * 0.9),
        Radius.circular(width * 0.008),
      ),
      pupilPaint,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(center: rightEyePos, width: width * 0.015, height: eyeHeight * 0.9),
        Radius.circular(width * 0.008),
      ),
      pupilPaint,
    );
    
    // Add multiple eye shines for realism
    final shinePaint = Paint()..color = Colors.white.withValues(alpha: 0.9);
    final smallShinePaint = Paint()..color = Colors.white.withValues(alpha: 0.6);
    
    // Main shine
    canvas.drawCircle(leftEyePos + Offset(-eyeWidth * 0.2, -eyeHeight * 0.3), width * 0.02, shinePaint);
    canvas.drawCircle(rightEyePos + Offset(-eyeWidth * 0.2, -eyeHeight * 0.3), width * 0.02, shinePaint);
    
    // Secondary shine
    canvas.drawCircle(leftEyePos + Offset(eyeWidth * 0.15, eyeHeight * 0.2), width * 0.01, smallShinePaint);
    canvas.drawCircle(rightEyePos + Offset(eyeWidth * 0.15, eyeHeight * 0.2), width * 0.01, smallShinePaint);
    
    // Eye outline for definition
    final eyeOutlinePaint = Paint()
      ..color = const Color(0xFF1B5E20)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    
    canvas.drawOval(
      Rect.fromCenter(center: leftEyePos, width: eyeWidth, height: eyeHeight),
      eyeOutlinePaint,
    );
    canvas.drawOval(
      Rect.fromCenter(center: rightEyePos, width: eyeWidth, height: eyeHeight),
      eyeOutlinePaint,
    );
  }

  void _drawRealisticNostrils(Canvas canvas, double width, double height) {
    final nostrilPaint = Paint()..color = Colors.black;
    final nostrilShadowPaint = Paint()
      ..color = const Color(0xFF0D2818)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2);
    
    // Nostril positions - on the tip of the snout
    final leftNostril = Offset(width * 0.45, -height * 0.15);
    final rightNostril = Offset(width * 0.45, height * 0.15);
    
    // Draw nostril shadows first
    canvas.drawOval(
      Rect.fromCenter(
        center: leftNostril + const Offset(1, 1),
        width: width * 0.04,
        height: height * 0.08,
      ),
      nostrilShadowPaint,
    );
    canvas.drawOval(
      Rect.fromCenter(
        center: rightNostril + const Offset(1, 1),
        width: width * 0.04,
        height: height * 0.08,
      ),
      nostrilShadowPaint,
    );
    
    // Draw main nostrils
    canvas.drawOval(
      Rect.fromCenter(
        center: leftNostril,
        width: width * 0.04,
        height: height * 0.08,
      ),
      nostrilPaint,
    );
    canvas.drawOval(
      Rect.fromCenter(
        center: rightNostril,
        width: width * 0.04,
        height: height * 0.08,
      ),
      nostrilPaint,
    );
  }

  void _drawRealisticMouth(Canvas canvas, double width, double height) {
    final time = DateTime.now().millisecondsSinceEpoch;
    
    // Mouth position at the tip of snout
    Offset mouthCenter = Offset(width * 0.48, 0);
    
    // Calculate mouth opening based on animation
    double mouthHeight = height * 0.03 + (mouthOpenAmount * height * 0.15);
    double mouthWidth = width * 0.02 + (mouthOpenAmount * width * 0.08);
    
    if (isMouthOpen || mouthOpenAmount > 0) {
      // Draw open mouth
      final mouthInteriorPaint = Paint()
        ..color = const Color(0xFF2C1810) // Dark interior
        ..style = PaintingStyle.fill;
      
      final mouthPath = Path();
      mouthPath.moveTo(mouthCenter.dx, mouthCenter.dy - mouthHeight);
      mouthPath.quadraticBezierTo(
        mouthCenter.dx + mouthWidth, mouthCenter.dy,
        mouthCenter.dx, mouthCenter.dy + mouthHeight,
      );
      mouthPath.quadraticBezierTo(
        mouthCenter.dx - mouthWidth * 0.3, mouthCenter.dy,
        mouthCenter.dx, mouthCenter.dy - mouthHeight,
      );
      mouthPath.close();
      
      canvas.drawPath(mouthPath, mouthInteriorPaint);
      
      // Draw mouth outline
      final mouthOutlinePaint = Paint()
        ..color = const Color(0xFF0D2818)
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke;
      
      canvas.drawPath(mouthPath, mouthOutlinePaint);
      
      // Draw teeth when mouth is open
      if (mouthOpenAmount > 0.5) {
        _drawTeeth(canvas, mouthCenter, mouthWidth, mouthHeight);
      }
    } else {
      // Draw closed mouth
      final mouthPaint = Paint()
        ..color = const Color(0xFF0D2818)
        ..strokeWidth = 2
        ..strokeCap = StrokeCap.round;
      
      canvas.drawLine(
        mouthCenter + Offset(0, -height * 0.02),
        mouthCenter + Offset(0, height * 0.02),
        mouthPaint,
      );
    }
    
    // Draw tongue (flicks out periodically from mouth)
    if (time % 3000 < 400 && !isMouthOpen) { // Only when mouth is closed
      _drawForkedTongue(canvas, mouthCenter, width, height, time);
    }
  }

  void _drawTeeth(Canvas canvas, Offset mouthCenter, double mouthWidth, double mouthHeight) {
    final toothPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.9)
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;
    
    // Draw upper fangs
    canvas.drawLine(
      mouthCenter + Offset(mouthWidth * 0.3, -mouthHeight * 0.8),
      mouthCenter + Offset(mouthWidth * 0.5, -mouthHeight * 0.3),
      toothPaint,
    );
    
    canvas.drawLine(
      mouthCenter + Offset(-mouthWidth * 0.1, -mouthHeight * 0.8),
      mouthCenter + Offset(-mouthWidth * 0.2, -mouthHeight * 0.3),
      toothPaint,
    );
    
    // Draw lower fangs
    canvas.drawLine(
      mouthCenter + Offset(mouthWidth * 0.3, mouthHeight * 0.8),
      mouthCenter + Offset(mouthWidth * 0.5, mouthHeight * 0.3),
      toothPaint,
    );
    
    canvas.drawLine(
      mouthCenter + Offset(-mouthWidth * 0.1, mouthHeight * 0.8),
      mouthCenter + Offset(-mouthWidth * 0.2, mouthHeight * 0.3),
      toothPaint,
    );
  }

  void _drawForkedTongue(Canvas canvas, Offset mouthCenter, double width, double height, int time) {
    final tonguePaint = Paint()
      ..color = const Color(0xFFD32F2F)
      ..strokeWidth = 2.5
      ..strokeCap = StrokeCap.round;
    
    final tongueBasePaint = Paint()
      ..color = const Color(0xFFB71C1C)
      ..strokeWidth = 3.5
      ..strokeCap = StrokeCap.round;
    
    double tongueLength = width * 0.3;
    double animationProgress = ((time % 3000) / 400.0).clamp(0.0, 1.0);
    double currentLength = tongueLength * animationProgress;
    
    // Tongue starts from mouth center and extends forward
    Offset tongueEnd = mouthCenter + Offset(currentLength, 0);
    
    // Draw tongue base from mouth
    canvas.drawLine(mouthCenter, tongueEnd, tongueBasePaint);
    
    // Draw forked tip when tongue is extended enough
    if (currentLength > tongueLength * 0.6) {
      double forkLength = currentLength * 0.25;
      
      // Upper fork
      canvas.drawLine(
        tongueEnd,
        tongueEnd + Offset(forkLength * 0.8, -forkLength * 0.6),
        tonguePaint,
      );
      
      // Lower fork
      canvas.drawLine(
        tongueEnd,
        tongueEnd + Offset(forkLength * 0.8, forkLength * 0.6),
        tonguePaint,
      );
    }
  }



  void _drawPulsatingFood(Canvas canvas, double cellWidth, double cellHeight) {
    final center = Offset(
      food.dx * cellWidth,
      food.dy * cellHeight,
    );

    final time = DateTime.now().millisecondsSinceEpoch;
    
    // Pulsating effect
    double pulseScale = 1.0 + sin(time * 0.008) * 0.2;
    double glowIntensity = 0.3 + sin(time * 0.01) * 0.2;
    
    // Draw outer glow
    final glowPaint = Paint()
      ..color = Colors.red.withValues(alpha: glowIntensity * 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

    canvas.drawCircle(center, cellWidth * 0.9 * pulseScale, glowPaint);

    // Draw middle glow
    final midGlowPaint = Paint()
      ..color = Colors.orange.withValues(alpha: glowIntensity * 0.5)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    canvas.drawCircle(center, cellWidth * 0.75 * pulseScale, midGlowPaint);

    // Draw apple with enhanced 3D effect
    final appleGradient = RadialGradient(
      center: const Alignment(-0.3, -0.3),
      colors: [
        Colors.red[200]!,
        Colors.red[500]!,
        Colors.red[800]!,
        Colors.red[900]!,
      ],
      stops: const [0.0, 0.4, 0.8, 1.0],
    );

    final applePaint = Paint()
      ..shader = appleGradient.createShader(
        Rect.fromCircle(center: center, radius: cellWidth * 0.6 * pulseScale),
      );

    canvas.drawCircle(center, cellWidth * 0.6 * pulseScale, applePaint);

    // Draw multiple highlights for more realism
    final highlight1Paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.6);

    canvas.drawCircle(
      center + Offset(-cellWidth * 0.15, -cellWidth * 0.15),
      cellWidth * 0.08 * pulseScale,
      highlight1Paint,
    );

    final highlight2Paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.3);

    canvas.drawCircle(
      center + Offset(cellWidth * 0.1, -cellWidth * 0.2),
      cellWidth * 0.05 * pulseScale,
      highlight2Paint,
    );

    // Draw enhanced stem with shadow
    final stemShadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.3)
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      center + Offset(1, -cellWidth * 0.4 * pulseScale + 1),
      center + Offset(1, -cellWidth * 0.65 * pulseScale + 1),
      stemShadowPaint,
    );

    final stemPaint = Paint()
      ..color = Colors.brown[700]!
      ..strokeWidth = 3
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      center + Offset(0, -cellWidth * 0.4 * pulseScale),
      center + Offset(0, -cellWidth * 0.65 * pulseScale),
      stemPaint,
    );

    // Draw enhanced leaf with gradient
    final leafGradient = LinearGradient(
      colors: [Colors.green[400]!, Colors.green[700]!],
    );

    final leafPaint = Paint()
      ..shader = leafGradient.createShader(
        Rect.fromCenter(
          center: center + Offset(cellWidth * 0.05, -cellWidth * 0.6 * pulseScale),
          width: cellWidth * 0.2,
          height: cellWidth * 0.3,
        ),
      );

    final leafPath = Path();
    leafPath.moveTo(center.dx, center.dy - cellWidth * 0.5 * pulseScale);
    leafPath.quadraticBezierTo(
      center.dx + cellWidth * 0.25,
      center.dy - cellWidth * 0.75 * pulseScale,
      center.dx + cellWidth * 0.15,
      center.dy - cellWidth * 0.85 * pulseScale,
    );
    leafPath.quadraticBezierTo(
      center.dx - cellWidth * 0.05,
      center.dy - cellWidth * 0.75 * pulseScale,
      center.dx,
      center.dy - cellWidth * 0.5 * pulseScale,
    );
    canvas.drawPath(leafPath, leafPaint);

    // Add sparkle effects
    _drawSparkles(canvas, center, cellWidth * pulseScale, time);
  }

  void _drawSparkles(Canvas canvas, Offset center, double size, int time) {
    final sparklePaint = Paint()
      ..color = Colors.yellow.withValues(alpha: 0.8)
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;

    // Rotating sparkles
    for (int i = 0; i < 4; i++) {
      double angle = (time * 0.005) + (i * pi / 2);
      double distance = size * 0.7;
      
      Offset sparklePos = center + Offset(
        cos(angle) * distance,
        sin(angle) * distance,
      );
      
      // Draw cross sparkle
      canvas.drawLine(
        sparklePos + const Offset(-3, 0),
        sparklePos + const Offset(3, 0),
        sparklePaint,
      );
      canvas.drawLine(
        sparklePos + const Offset(0, -3),
        sparklePos + const Offset(0, 3),
        sparklePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

// Extension to normalize Offset
extension OffsetExtension on Offset {
  Offset normalize() {
    double length = distance;
    if (length == 0) return Offset.zero;
    return this / length;
  }
}