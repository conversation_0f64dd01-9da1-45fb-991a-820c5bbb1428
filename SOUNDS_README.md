# نظام الأصوات في لعبة الثعبان

## الأصوات المضافة

تم إضافة نظام أصوات شامل للعبة يتضمن الأصوات التالية:

### 1. أصوات اللعبة الأساسية
- **صوت الأكل (eat)**: يُشغل عند أكل الثعبان للطعام
- **صوت ظهور الطعام (fruit)**: يُشغل عند ظهور طعام جديد
- **صوت بداية اللعبة (gameStart)**: يُشغل عند بدء لعبة جديدة
- **صوت الحركة (move)**: صوت خفيف يُشغل أحياناً أثناء حركة الثعبان

### 2. أصوات التفاعل
- **صوت النقر على الأزرار (buttonClick)**: يُشغل عند النقر على أي زر في اللعبة
- **صوت الاصطدام (collision)**: يُشغل عند اصطدام الثعبان بالحائط أو بنفسه
- **صوت انتهاء اللعبة (gameOver)**: صوت درامي يُشغل عند انتهاء اللعبة

### 3. أصوات الإنجازات
- **صوت ترقية المستوى (levelUp)**: يُشغل عند الانتقال لمستوى جديد
- **صوت النقاط العالية (highScore)**: يُشغل عند الوصول لنقاط مضاعفة للـ 100
- **صوت إكمال المستوى**: يُشغل عند إكمال متطلبات المستوى بنجاح

## المميزات التقنية

### 1. توليد الأصوات برمجياً
- جميع الأصوات يتم توليدها برمجياً باستخدام موجات صوتية
- لا حاجة لملفات صوتية خارجية
- أصوات عالية الجودة بتردد 44.1 kHz

### 2. نظام Fallback
- في حالة فشل تشغيل الأصوات المخصصة، يتم استخدام أصوات النظام
- يتضمن اهتزاز تكتيكي (Haptic Feedback) للتفاعل الحسي

### 3. إدارة الأصوات
- إمكانية تشغيل/إيقاف الأصوات من الإعدادات
- تحكم ذكي في مستوى الصوت
- تنظيف تلقائي للموارد

## كيفية عمل النظام

### 1. التهيئة
```dart
await SoundManager.initialize(soundEnabled: true);
```

### 2. تشغيل الأصوات
```dart
await SoundManager.playSound('eat');
```

### 3. التحكم في الإعدادات
```dart
SoundManager.setSoundEnabled(false); // إيقاف الأصوات
```

## الملفات المعدلة

### 1. ملفات جديدة
- `lib/sound_manager.dart`: مدير الأصوات الرئيسي

### 2. ملفات محدثة
- `lib/main.dart`: إضافة استدعاءات الأصوات
- `lib/screens/game_over_screen.dart`: أصوات الأزرار
- `lib/screens/settings_screen.dart`: أصوات الإعدادات
- `lib/screens/levels_screen.dart`: أصوات اختيار المستوى
- `pubspec.yaml`: إضافة dependencies جديدة

### 3. Dependencies المضافة
- `audioplayers: ^6.0.0`: لتشغيل الأصوات
- `path_provider: ^2.1.4`: للوصول لمجلد الملفات المؤقتة

## الاستخدام

الأصوات تعمل تلقائياً في اللعبة. يمكن التحكم فيها من خلال:

1. **الإعدادات**: تشغيل/إيقاف الأصوات
2. **الاهتزاز**: تشغيل/إيقاف الاهتزاز التكتيكي
3. **سرعة اللعبة**: تؤثر على توقيت بعض الأصوات

## ملاحظات تقنية

- الأصوات يتم حفظها في مجلد الملفات المؤقتة
- يتم إنشاء الأصوات عند أول تشغيل للتطبيق
- النظام متوافق مع Android و iOS
- استهلاك ذاكرة منخفض
- أداء محسن للألعاب

## المطور

تم تطوير نظام الأصوات بواسطة AI Assistant لتحسين تجربة لعبة الثعبان.