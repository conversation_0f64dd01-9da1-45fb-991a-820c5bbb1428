#!/usr/bin/env python3
"""
<PERSON>ript to update Android app icon with the new custom icon
"""

import os
import shutil
from pathlib import Path

def update_android_icon():
    """Update Android app icons with the new custom icon"""
    
    # Source icon path
    source_icon = "assets/icons/٢٠٢٥٠٦٢٩_٢٠٢٥٥٦.jpg"
    
    if not os.path.exists(source_icon):
        print(f"❌ Source icon not found: {source_icon}")
        return False
    
    # Android icon directories and their required sizes
    android_dirs = {
        "android/app/src/main/res/mipmap-mdpi": 48,
        "android/app/src/main/res/mipmap-hdpi": 72,
        "android/app/src/main/res/mipmap-xhdpi": 96,
        "android/app/src/main/res/mipmap-xxhdpi": 144,
        "android/app/src/main/res/mipmap-xxxhdpi": 192,
    }
    
    try:
        from PIL import Image
        
        print("🎨 Processing custom icon...")
        
        # Load the source image
        with Image.open(source_icon) as img:
            # Convert to RGBA if needed
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            print(f"📏 Original icon size: {img.size}")
            
            # Create icons for each Android density
            for android_dir, size in android_dirs.items():
                # Ensure directory exists
                os.makedirs(android_dir, exist_ok=True)
                
                # Resize image
                resized_img = img.resize((size, size), Image.Resampling.LANCZOS)
                
                # Save as PNG
                output_path = os.path.join(android_dir, "ic_launcher.png")
                resized_img.save(output_path, "PNG")
                
                print(f"✅ Created: {output_path} ({size}x{size})")
        
        print("🚀 Android icons updated successfully!")
        return True
        
    except ImportError:
        print("⚠️  PIL (Pillow) not available. Copying original file...")
        
        # Fallback: just copy the original file to all directories
        for android_dir in android_dirs.keys():
            os.makedirs(android_dir, exist_ok=True)
            output_path = os.path.join(android_dir, "ic_launcher.png")
            
            # Convert JPG to PNG using system tools if available
            try:
                import subprocess
                # Try using ImageMagick convert command
                subprocess.run([
                    "magick", "convert", source_icon, 
                    "-resize", f"{android_dirs[android_dir]}x{android_dirs[android_dir]}", 
                    output_path
                ], check=True)
                print(f"✅ Converted: {output_path}")
            except (subprocess.CalledProcessError, FileNotFoundError):
                # If convert fails, just copy the original
                shutil.copy2(source_icon, output_path)
                print(f"📋 Copied: {output_path} (original size)")
        
        return True
    
    except Exception as e:
        print(f"❌ Error processing icon: {e}")
        return False

def main():
    print("🔄 Updating app icon...")
    
    if update_android_icon():
        print("\n✅ Icon update completed!")
        print("🏗️  Now building the app with new icon...")
        
        # Clean and rebuild
        os.system("flutter clean")
        os.system("flutter pub get")
        os.system("flutter build apk --release")
        
        print("🎉 App built with new icon!")
        
        # Copy the new APK with a descriptive name
        if os.path.exists("build/app/outputs/flutter-apk/app-release.apk"):
            shutil.copy2(
                "build/app/outputs/flutter-apk/app-release.apk",
                "SnakeGame_CustomIcon.apk"
            )
            print("📱 New APK created: SnakeGame_CustomIcon.apk")
    else:
        print("❌ Icon update failed!")

if __name__ == "__main__":
    main()
