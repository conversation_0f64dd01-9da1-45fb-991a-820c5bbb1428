import 'package:flutter/material.dart';

enum ObstacleType {
  rock,    // Rocks
  pillar,  // Pillars
  tree,    // Trees
}

class ObstacleData {
  final ObstacleType type;
  final Offset position;
  final double size;
  final double rotation;

  ObstacleData({
    required this.type,
    required this.position,
    this.size = 1.0,
    this.rotation = 0.0,
  });
}

class LevelData {
  final int level;
  final String name;
  final String description;
  final String difficulty;
  final int speed; // milliseconds - lower = faster
  final List<ObstacleData> obstacles;
  final int requiredScore;
  final bool isUnlocked;
  final Color backgroundColor;
  final Color grassColor;

  LevelData({
    required this.level,
    required this.name,
    required this.description,
    required this.difficulty,
    required this.speed,
    required this.obstacles,
    required this.requiredScore,
    required this.isUnlocked,
    this.backgroundColor = const Color(0xFF2E7D32),
    this.grassColor = const Color(0xFF4CAF50),
  });

  // Copy level with updated unlock status
  LevelData copyWith({
    bool? isUnlocked,
  }) {
    return LevelData(
      level: level,
      name: name,
      description: description,
      difficulty: difficulty,
      speed: speed,
      obstacles: obstacles,
      requiredScore: requiredScore,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      backgroundColor: backgroundColor,
      grassColor: grassColor,
    );
  }
}

// Game Settings
class GameSettings {
  final bool soundEnabled;
  final bool vibrationEnabled;
  final double gameSpeed;
  final String difficulty;

  GameSettings({
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.gameSpeed = 1.0,
    this.difficulty = 'Medium',
  });

  GameSettings copyWith({
    bool? soundEnabled,
    bool? vibrationEnabled,
    double? gameSpeed,
    String? difficulty,
  }) {
    return GameSettings(
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      gameSpeed: gameSpeed ?? this.gameSpeed,
      difficulty: difficulty ?? this.difficulty,
    );
  }
}