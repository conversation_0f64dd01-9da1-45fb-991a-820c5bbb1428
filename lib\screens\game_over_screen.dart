import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/level_data.dart';
import '../sound_manager.dart';

class GameOverScreen extends StatelessWidget {
  final int score;
  final LevelData? levelData;
  final bool isLevelComplete;

  const GameOverScreen({
    super.key,
    required this.score,
    this.levelData,
    this.isLevelComplete = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.8),
      body: Center(
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(30),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: isLevelComplete
                  ? [
                      const Color(0xFF4CAF50),
                      const Color(0xFF2E7D32),
                      const Color(0xFF1B5E20),
                    ]
                  : [
                      const Color(0xFFE53935),
                      const Color(0xFFC62828),
                      const Color(0xFFB71C1C),
                    ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.5),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Result Icon
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isLevelComplete ? Icons.check_circle : Icons.cancel,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Result Title
              Text(
                isLevelComplete ? 'Congratulations!' : 'Game Over',
                style: const TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 10),
              
              // Level Info
              if (levelData != null) ...[
                Text(
                  levelData!.name,
                  style: const TextStyle(
                    fontSize: 20,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 5),
              ],
              
              // Score
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  children: [
                    const Text(
                      'SCORE',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                    ),
                    Text(
                      '$score',
                      style: const TextStyle(
                        fontSize: 36,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Level Progress
              if (levelData != null && isLevelComplete) ...[
                Container(
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'Next Level Unlocked!',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        'Level ${levelData!.level + 1}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
              ],
              
              // Buttons
              Row(
                children: [
                  Expanded(
                    child: _buildButton(
                      title: 'Try Again',
                      icon: Icons.refresh,
                      onTap: () {
                        Navigator.pop(context, 'retry');
                      },
                    ),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: _buildButton(
                      title: 'Main Menu',
                      icon: Icons.home,
                      onTap: () {
                        Navigator.pop(context, 'home');
                      },
                    ),
                  ),
                ],
              ),
              
              if (isLevelComplete && levelData != null) ...[
                const SizedBox(height: 15),
                SizedBox(
                  width: double.infinity,
                  child: _buildButton(
                    title: 'Next Level',
                    icon: Icons.arrow_forward,
                    onTap: () {
                      Navigator.pop(context, 'next_level');
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildButton({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            HapticFeedback.lightImpact();
            // Play button click sound
            try {
              await SoundManager.playSound('buttonClick');
            } catch (e) {
              // Continue if sound fails
            }
            onTap();
          },
          borderRadius: BorderRadius.circular(10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}