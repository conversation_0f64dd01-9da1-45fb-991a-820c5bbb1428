# PowerShell script to convert custom icon using .NET System.Drawing
param(
    [string]$SourcePath = "assets\icons\٢٠٢٥٠٦٢٩_٢٠٢٥٥٦.jpg"
)

Write-Host "🎨 Converting your custom icon to PNG format..." -ForegroundColor Green

# Check if source file exists
if (-not (Test-Path $SourcePath)) {
    Write-Host "❌ Source icon not found: $SourcePath" -ForegroundColor Red
    exit 1
}

Write-Host "📁 Found source icon: $SourcePath" -ForegroundColor Yellow

# Load System.Drawing assembly
Add-Type -AssemblyName System.Drawing

try {
    # Load the source image
    $sourceImage = [System.Drawing.Image]::FromFile((Resolve-Path $SourcePath).Path)
    Write-Host "📏 Original size: $($sourceImage.Width)x$($sourceImage.Height)" -ForegroundColor Cyan
    
    # Define Android icon sizes and directories
    $iconSizes = @{
        "android\app\src\main\res\mipmap-mdpi" = 48
        "android\app\src\main\res\mipmap-hdpi" = 72
        "android\app\src\main\res\mipmap-xhdpi" = 96
        "android\app\src\main\res\mipmap-xxhdpi" = 144
        "android\app\src\main\res\mipmap-xxxhdpi" = 192
    }
    
    foreach ($dir in $iconSizes.Keys) {
        $size = $iconSizes[$dir]
        $outputPath = Join-Path $dir "ic_launcher.png"
        
        # Create directory if it doesn't exist
        $dirPath = Split-Path $outputPath -Parent
        if (-not (Test-Path $dirPath)) {
            New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
        }
        
        # Create resized bitmap
        $resizedBitmap = New-Object System.Drawing.Bitmap($size, $size)
        $graphics = [System.Drawing.Graphics]::FromImage($resizedBitmap)
        
        # Set high quality settings
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
        $graphics.PixelOffsetMode = [System.Drawing.Drawing2D.PixelOffsetMode]::HighQuality
        $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
        
        # Draw resized image
        $graphics.DrawImage($sourceImage, 0, 0, $size, $size)
        
        # Save as PNG
        $resizedBitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)
        
        # Cleanup
        $graphics.Dispose()
        $resizedBitmap.Dispose()
        
        Write-Host "✅ Created: $outputPath ($($size)x$($size))" -ForegroundColor Green
    }
    
    # Cleanup source image
    $sourceImage.Dispose()
    
    Write-Host "🎉 Custom icon conversion completed successfully!" -ForegroundColor Green
    Write-Host "🚀 Ready to build app with your custom icon!" -ForegroundColor Yellow

}
catch {
    Write-Host "❌ Error converting icon: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
