import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/level_data.dart';
import '../main.dart';
import '../sound_manager.dart';

class LevelsScreen extends StatefulWidget {
  const LevelsScreen({super.key});

  @override
  State<LevelsScreen> createState() => _LevelsScreenState();
}

class _LevelsScreenState extends State<LevelsScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final List<LevelData> levels = [
    LevelData(
      level: 1,
      name: 'Peaceful Garden',
      description: 'Start your journey in a quiet garden',
      difficulty: 'Easy',
      speed: 150,
      obstacles: [],
      requiredScore: 50,
      isUnlocked: true,
    ),
    LevelData(
      level: 2,
      name: 'Rocky Path',
      description: 'Avoid the small rocks',
      difficulty: 'Easy',
      speed: 140,
      obstacles: [
        ObstacleData(type: ObstacleType.rock, position: Offset(10, 8)),
        ObstacleData(type: ObstacleType.rock, position: Offset(20, 12)),
      ],
      requiredScore: 100,
      isUnlocked: true,
    ),
    LevelData(
      level: 3,
      name: 'Pillar Forest',
      description: 'Wooden pillars block your way',
      difficulty: 'Medium',
      speed: 130,
      obstacles: [
        ObstacleData(type: ObstacleType.pillar, position: Offset(8, 6)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(22, 14)),
        ObstacleData(type: ObstacleType.rock, position: Offset(15, 10)),
      ],
      requiredScore: 150,
      isUnlocked: false,
    ),
    LevelData(
      level: 4,
      name: 'Tree Field',
      description: 'Dense trees everywhere',
      difficulty: 'Medium',
      speed: 120,
      obstacles: [
        ObstacleData(type: ObstacleType.tree, position: Offset(7, 5)),
        ObstacleData(type: ObstacleType.tree, position: Offset(23, 15)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(15, 8)),
        ObstacleData(type: ObstacleType.rock, position: Offset(12, 12)),
      ],
      requiredScore: 200,
      isUnlocked: false,
    ),
    LevelData(
      level: 5,
      name: 'Stone Maze',
      description: 'Large rocks form a maze',
      difficulty: 'Medium',
      speed: 110,
      obstacles: [
        ObstacleData(type: ObstacleType.rock, position: Offset(5, 5)),
        ObstacleData(type: ObstacleType.rock, position: Offset(10, 5)),
        ObstacleData(type: ObstacleType.rock, position: Offset(15, 5)),
        ObstacleData(type: ObstacleType.rock, position: Offset(20, 5)),
        ObstacleData(type: ObstacleType.rock, position: Offset(25, 5)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(8, 12)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(22, 12)),
      ],
      requiredScore: 250,
      isUnlocked: false,
    ),
    LevelData(
      level: 6,
      name: 'Dark Forest',
      description: 'Trees and pillars everywhere',
      difficulty: 'Hard',
      speed: 100,
      obstacles: [
        ObstacleData(type: ObstacleType.tree, position: Offset(6, 4)),
        ObstacleData(type: ObstacleType.tree, position: Offset(14, 6)),
        ObstacleData(type: ObstacleType.tree, position: Offset(22, 8)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(10, 12)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(18, 14)),
        ObstacleData(type: ObstacleType.rock, position: Offset(8, 8)),
        ObstacleData(type: ObstacleType.rock, position: Offset(16, 10)),
        ObstacleData(type: ObstacleType.rock, position: Offset(24, 12)),
      ],
      requiredScore: 300,
      isUnlocked: false,
    ),
    LevelData(
      level: 7,
      name: 'Rock Valley',
      description: 'Giant rocks everywhere',
      difficulty: 'Hard',
      speed: 90,
      obstacles: [
        ObstacleData(type: ObstacleType.rock, position: Offset(4, 4)),
        ObstacleData(type: ObstacleType.rock, position: Offset(8, 6)),
        ObstacleData(type: ObstacleType.rock, position: Offset(12, 4)),
        ObstacleData(type: ObstacleType.rock, position: Offset(16, 8)),
        ObstacleData(type: ObstacleType.rock, position: Offset(20, 6)),
        ObstacleData(type: ObstacleType.rock, position: Offset(24, 4)),
        ObstacleData(type: ObstacleType.tree, position: Offset(6, 12)),
        ObstacleData(type: ObstacleType.tree, position: Offset(18, 14)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(12, 16)),
      ],
      requiredScore: 350,
      isUnlocked: false,
    ),
    LevelData(
      level: 8,
      name: 'Dense Jungle',
      description: 'Jungle full of obstacles',
      difficulty: 'Very Hard',
      speed: 80,
      obstacles: [
        ObstacleData(type: ObstacleType.tree, position: Offset(5, 3)),
        ObstacleData(type: ObstacleType.tree, position: Offset(9, 5)),
        ObstacleData(type: ObstacleType.tree, position: Offset(13, 3)),
        ObstacleData(type: ObstacleType.tree, position: Offset(17, 7)),
        ObstacleData(type: ObstacleType.tree, position: Offset(21, 5)),
        ObstacleData(type: ObstacleType.tree, position: Offset(25, 3)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(7, 11)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(15, 13)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(23, 11)),
        ObstacleData(type: ObstacleType.rock, position: Offset(11, 9)),
        ObstacleData(type: ObstacleType.rock, position: Offset(19, 15)),
      ],
      requiredScore: 400,
      isUnlocked: false,
    ),
    LevelData(
      level: 9,
      name: 'Lost Temple',
      description: 'Ancient temple pillars',
      difficulty: 'Expert',
      speed: 70,
      obstacles: [
        ObstacleData(type: ObstacleType.pillar, position: Offset(6, 4)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(12, 4)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(18, 4)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(24, 4)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(6, 12)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(12, 12)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(18, 12)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(24, 12)),
        ObstacleData(type: ObstacleType.rock, position: Offset(9, 8)),
        ObstacleData(type: ObstacleType.rock, position: Offset(15, 8)),
        ObstacleData(type: ObstacleType.rock, position: Offset(21, 8)),
        ObstacleData(type: ObstacleType.tree, position: Offset(3, 8)),
        ObstacleData(type: ObstacleType.tree, position: Offset(27, 8)),
      ],
      requiredScore: 450,
      isUnlocked: false,
    ),
    LevelData(
      level: 10,
      name: 'Final Challenge',
      description: 'Hardest level - all obstacles together',
      difficulty: 'Impossible',
      speed: 60,
      obstacles: [
        // Top row of rocks
        ObstacleData(type: ObstacleType.rock, position: Offset(4, 3)),
        ObstacleData(type: ObstacleType.rock, position: Offset(8, 3)),
        ObstacleData(type: ObstacleType.rock, position: Offset(12, 3)),
        ObstacleData(type: ObstacleType.rock, position: Offset(16, 3)),
        ObstacleData(type: ObstacleType.rock, position: Offset(20, 3)),
        ObstacleData(type: ObstacleType.rock, position: Offset(24, 3)),
        // Middle pillars
        ObstacleData(type: ObstacleType.pillar, position: Offset(6, 8)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(14, 8)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(22, 8)),
        // Bottom trees
        ObstacleData(type: ObstacleType.tree, position: Offset(5, 13)),
        ObstacleData(type: ObstacleType.tree, position: Offset(11, 13)),
        ObstacleData(type: ObstacleType.tree, position: Offset(17, 13)),
        ObstacleData(type: ObstacleType.tree, position: Offset(23, 13)),
        // Additional obstacles
        ObstacleData(type: ObstacleType.rock, position: Offset(10, 6)),
        ObstacleData(type: ObstacleType.rock, position: Offset(18, 10)),
        ObstacleData(type: ObstacleType.pillar, position: Offset(26, 6)),
        ObstacleData(type: ObstacleType.tree, position: Offset(2, 10)),
      ],
      requiredScore: 500,
      isUnlocked: false,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1B5E20),
      appBar: AppBar(
        title: const Text(
          'Levels',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF2E7D32),
              Color(0xFF1B5E20),
              Color(0xFF0D2818),
            ],
          ),
        ),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: GridView.builder(
            padding: const EdgeInsets.all(20),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
              crossAxisSpacing: 15,
              mainAxisSpacing: 15,
            ),
            itemCount: levels.length,
            itemBuilder: (context, index) {
              return _buildLevelCard(levels[index]);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildLevelCard(LevelData level) {
    Color cardColor = level.isUnlocked 
        ? Colors.white.withValues(alpha: 0.15)
        : Colors.grey.withValues(alpha: 0.1);
    
    Color difficultyColor = _getDifficultyColor(level.difficulty);
    
    return GestureDetector(
      onTap: () async {
        if (level.isUnlocked) {
          HapticFeedback.lightImpact();
          try {
            await SoundManager.playSound('buttonClick');
          } catch (e) {
            // Continue if sound fails
          }
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => GameScreen(levelData: level),
              ),
            );
          }
        } else {
          try {
            await SoundManager.playSound('buttonClick');
          } catch (e) {
            // Continue if sound fails
          }
          if (mounted) {
            _showLockedDialog(level);
          }
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: cardColor,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: level.isUnlocked 
                ? difficultyColor.withValues(alpha: 0.5)
                : Colors.grey.withValues(alpha: 0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(15),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Level Number
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: level.isUnlocked ? difficultyColor : Colors.grey,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: level.isUnlocked
                      ? Text(
                          '${level.level}',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(
                          Icons.lock,
                          color: Colors.white,
                          size: 24,
                        ),
                ),
              ),
              
              // Level Info
              Column(
                children: [
                  Text(
                    level.name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: level.isUnlocked ? Colors.white : Colors.grey[400],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 5),
                  Text(
                    level.description,
                    style: TextStyle(
                      fontSize: 12,
                      color: level.isUnlocked 
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              
              // Difficulty Badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: difficultyColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: difficultyColor.withValues(alpha: 0.5),
                  ),
                ),
                child: Text(
                  level.difficulty,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: level.isUnlocked ? difficultyColor : Colors.grey,
                  ),
                ),
              ),
              
              // Obstacles Count
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.warning,
                    size: 16,
                    color: level.isUnlocked 
                        ? Colors.orange[300]
                        : Colors.grey[500],
                  ),
                  const SizedBox(width: 5),
                  Text(
                    '${level.obstacles.length} Obstacles',
                    style: TextStyle(
                      fontSize: 12,
                      color: level.isUnlocked 
                          ? Colors.white.withValues(alpha: 0.8)
                          : Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case 'Easy':
        return Colors.green;
      case 'Medium':
        return Colors.orange;
      case 'Hard':
        return Colors.red;
      case 'Very Hard':
        return Colors.purple;
      case 'Expert':
        return Colors.indigo;
      case 'Impossible':
        return Colors.black;
      default:
        return Colors.grey;
    }
  }

  void _showLockedDialog(LevelData level) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF2E7D32),
        title: const Text(
          'Level Locked',
          style: TextStyle(color: Colors.white),
        ),
        content: Text(
          'You need ${level.requiredScore} points to unlock this level',
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'OK',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}