Write-Host "Converting custom icon..." -ForegroundColor Green

# Load System.Drawing
Add-Type -AssemblyName System.Drawing

# Load source image
$sourcePath = "assets\icons\app_icon.jpg"
$sourceImage = [System.Drawing.Image]::FromFile((Resolve-Path $sourcePath).Path)

Write-Host "Original size: $($sourceImage.Width)x$($sourceImage.Height)" -ForegroundColor Yellow

# Create 72x72 icon for hdpi
$size = 72
$resizedBitmap = New-Object System.Drawing.Bitmap($size, $size)
$graphics = [System.Drawing.Graphics]::FromImage($resizedBitmap)
$graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
$graphics.DrawImage($sourceImage, 0, 0, $size, $size)

# Save as PNG
$outputPath = "android\app\src\main\res\mipmap-hdpi\ic_launcher.png"
$resizedBitmap.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Png)

Write-Host "Created: $outputPath" -ForegroundColor Green

# Cleanup
$graphics.Dispose()
$resizedBitmap.Dispose()
$sourceImage.Dispose()

Write-Host "Done!" -ForegroundColor Green
