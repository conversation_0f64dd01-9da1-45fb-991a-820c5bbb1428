#!/usr/bin/env python3
"""
Simple script to create a basic app icon for the Snake Game
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import math
    
    def create_snake_icon(size=1024):
        # Create image with transparent background
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Background gradient (dark green circle)
        center = size // 2
        for r in range(center, 0, -1):
            alpha = int(255 * (1 - r / center))
            green_intensity = int(45 + (r / center) * 30)
            color = (0, green_intensity, 0, alpha)
            draw.ellipse([center - r, center - r, center + r, center + r], 
                        fill=color, outline=None)
        
        # Snake body (spiral)
        snake_segments = []
        for i in range(12):
            angle = i * 0.8
            radius = size * 0.15 + i * size * 0.02
            x = center + radius * math.cos(angle)
            y = center + radius * math.sin(angle)
            snake_segments.append((x, y))
        
        # Draw snake segments
        for i, (x, y) in enumerate(snake_segments):
            segment_size = size * 0.04 - i * size * 0.002
            # Body
            draw.ellipse([x - segment_size, y - segment_size, 
                         x + segment_size, y + segment_size], 
                        fill=(76, 175, 80, 255), outline=(46, 125, 50, 255))
            # Highlight
            highlight_size = segment_size * 0.3
            draw.ellipse([x - segment_size * 0.5, y - segment_size * 0.5,
                         x - segment_size * 0.5 + highlight_size * 2, 
                         y - segment_size * 0.5 + highlight_size * 2],
                        fill=(102, 187, 106, 200))
        
        # Snake head
        if snake_segments:
            head_x, head_y = snake_segments[0]
            head_size = size * 0.05
            
            # Head
            draw.ellipse([head_x - head_size, head_y - head_size,
                         head_x + head_size, head_y + head_size],
                        fill=(76, 175, 80, 255), outline=(46, 125, 50, 255))
            
            # Eyes
            eye_size = head_size * 0.15
            # Left eye
            draw.ellipse([head_x - head_size * 0.3 - eye_size, 
                         head_y - head_size * 0.2 - eye_size,
                         head_x - head_size * 0.3 + eye_size, 
                         head_y - head_size * 0.2 + eye_size],
                        fill=(255, 255, 255, 255))
            draw.ellipse([head_x - head_size * 0.3 - eye_size * 0.5, 
                         head_y - head_size * 0.2 - eye_size * 0.5,
                         head_x - head_size * 0.3 + eye_size * 0.5, 
                         head_y - head_size * 0.2 + eye_size * 0.5],
                        fill=(0, 0, 0, 255))
            
            # Right eye
            draw.ellipse([head_x + head_size * 0.3 - eye_size, 
                         head_y - head_size * 0.2 - eye_size,
                         head_x + head_size * 0.3 + eye_size, 
                         head_y - head_size * 0.2 + eye_size],
                        fill=(255, 255, 255, 255))
            draw.ellipse([head_x + head_size * 0.3 - eye_size * 0.5, 
                         head_y - head_size * 0.2 - eye_size * 0.5,
                         head_x + head_size * 0.3 + eye_size * 0.5, 
                         head_y - head_size * 0.2 + eye_size * 0.5],
                        fill=(0, 0, 0, 255))
        
        # Apple/Food
        apple_x, apple_y = size * 0.75, size * 0.25
        apple_size = size * 0.04
        
        # Apple body
        draw.ellipse([apple_x - apple_size, apple_y - apple_size,
                     apple_x + apple_size, apple_y + apple_size],
                    fill=(229, 57, 53, 255))
        
        # Apple highlight
        highlight_size = apple_size * 0.4
        draw.ellipse([apple_x - apple_size * 0.3 - highlight_size, 
                     apple_y - apple_size * 0.3 - highlight_size,
                     apple_x - apple_size * 0.3 + highlight_size, 
                     apple_y - apple_size * 0.3 + highlight_size],
                    fill=(255, 87, 34, 200))
        
        return img
    
    # Create different sizes
    sizes = [1024, 512, 256, 128, 72, 48]
    
    for size in sizes:
        print(f"Creating icon {size}x{size}...")
        icon = create_snake_icon(size)
        
        if size == 1024:
            icon.save('assets/icons/app_icon.png', 'PNG')
        else:
            icon.save(f'assets/icons/app_icon_{size}.png', 'PNG')
    
    print("✅ All icons created successfully!")
    print("📁 Icons saved in assets/icons/")
    
except ImportError:
    print("❌ PIL (Pillow) not installed. Please install it with:")
    print("pip install Pillow")
    print("\nAlternatively, you can manually add your icon files to assets/icons/")
    
    # Create a simple placeholder file
    with open('assets/icons/PLACEHOLDER.txt', 'w') as f:
        f.write("""
🎨 ICON PLACEHOLDER

Please add your app icon files here:
- app_icon.png (1024x1024) - Main icon
- app_icon_512.png (512x512) - Optional
- app_icon_256.png (256x256) - Optional

The icon should represent a snake game with:
- Green snake 🐍
- Red apple 🍎
- Dark background

After adding the icon, run:
flutter clean
flutter pub get
flutter build apk --release
        """)
    print("📝 Created placeholder file in assets/icons/")
