import 'package:flutter/material.dart';
import 'dart:math';
import '../models/level_data.dart';

class ObstaclePainter {
  
  // Draw realistic 3D rock
  static void drawRealisticRock(Canvas canvas, Offset position, double cellWidth, double cellHeight, {double size = 1.0}) {
    final center = Offset(
      position.dx * cellWidth + cellWidth / 2,
      position.dy * cellHeight + cellHeight / 2,
    );
    
    final rockSize = cellWidth * 0.8 * size;
    
    // Rock shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.4)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);
    
    canvas.drawOval(
      Rect.fromCenter(
        center: center + const Offset(3, 3),
        width: rockSize * 1.2,
        height: rockSize * 0.6,
      ),
      shadowPaint,
    );
    
    // Main rock - 3D gradient
    final rockGradient = RadialGradient(
      center: const Alignment(-0.3, -0.3),
      radius: 0.8,
      colors: [
        const Color(0xFF8D6E63), // Light brown
        const Color(0xFF6D4C41), // Medium brown
        const Color(0xFF5D4037), // Dark brown
        const Color(0xFF3E2723), // Very dark brown
      ],
      stops: const [0.0, 0.4, 0.7, 1.0],
    );
    
    final rockPaint = Paint()
      ..shader = rockGradient.createShader(
        Rect.fromCenter(center: center, width: rockSize, height: rockSize),
      );
    
    // Irregular rock shape
    final rockPath = Path();
    final random = Random(position.dx.toInt() + position.dy.toInt());
    
    for (int i = 0; i < 8; i++) {
      double angle = (i / 8) * 2 * pi;
      double radius = rockSize / 2 * (0.8 + random.nextDouble() * 0.4);
      double x = center.dx + cos(angle) * radius;
      double y = center.dy + sin(angle) * radius * 0.8; // Slightly flattened
      
      if (i == 0) {
        rockPath.moveTo(x, y);
      } else {
        rockPath.lineTo(x, y);
      }
    }
    rockPath.close();
    
    canvas.drawPath(rockPath, rockPaint);
    
    // Rock details - lines and cracks
    final crackPaint = Paint()
      ..color = const Color(0xFF3E2723).withValues(alpha: 0.6)
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;
    
    // Random cracks
    for (int i = 0; i < 3; i++) {
      double startAngle = random.nextDouble() * 2 * pi;
      double length = rockSize * (0.2 + random.nextDouble() * 0.3);
      Offset start = center + Offset(
        cos(startAngle) * rockSize * 0.2,
        sin(startAngle) * rockSize * 0.2,
      );
      Offset end = start + Offset(
        cos(startAngle) * length,
        sin(startAngle) * length,
      );
      
      canvas.drawLine(start, end, crackPaint);
    }
    
    // Highlight on rock
    final highlightPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.3)
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;
    
    canvas.drawLine(
      center + Offset(-rockSize * 0.2, -rockSize * 0.3),
      center + Offset(rockSize * 0.1, -rockSize * 0.2),
      highlightPaint,
    );
  }
  
  // Draw realistic wooden pillar
  static void drawRealisticPillar(Canvas canvas, Offset position, double cellWidth, double cellHeight, {double size = 1.0}) {
    final center = Offset(
      position.dx * cellWidth + cellWidth / 2,
      position.dy * cellHeight + cellHeight / 2,
    );
    
    final pillarWidth = cellWidth * 0.6 * size;
    final pillarHeight = cellHeight * 1.5 * size;
    
    // Pillar shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.5)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);
    
    canvas.drawRect(
      Rect.fromCenter(
        center: center + const Offset(4, 4),
        width: pillarWidth * 1.1,
        height: pillarHeight,
      ),
      shadowPaint,
    );
    
    // Main pillar - brown wood
    final pillarGradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        const Color(0xFF8D6E63), // Light brown
        const Color(0xFF795548), // Medium brown
        const Color(0xFF6D4C41), // Dark brown
        const Color(0xFF5D4037), // Very dark brown
      ],
      stops: const [0.0, 0.3, 0.7, 1.0],
    );
    
    final pillarPaint = Paint()
      ..shader = pillarGradient.createShader(
        Rect.fromCenter(center: center, width: pillarWidth, height: pillarHeight),
      );
    
    // Draw pillar
    final pillarRect = Rect.fromCenter(
      center: center,
      width: pillarWidth,
      height: pillarHeight,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(pillarRect, const Radius.circular(8)),
      pillarPaint,
    );
    
    // Horizontal wood grain lines
    final woodGrainPaint = Paint()
      ..color = const Color(0xFF5D4037).withValues(alpha: 0.4)
      ..strokeWidth = 1;
    
    for (int i = 0; i < 6; i++) {
      double y = pillarRect.top + (pillarRect.height / 6) * (i + 0.5);
      canvas.drawLine(
        Offset(pillarRect.left, y),
        Offset(pillarRect.right, y),
        woodGrainPaint,
      );
    }
    
    // Vertical wood grain lines
    final verticalGrainPaint = Paint()
      ..color = const Color(0xFF6D4C41).withValues(alpha: 0.3)
      ..strokeWidth = 0.8;
    
    for (int i = 0; i < 3; i++) {
      double x = pillarRect.left + (pillarRect.width / 4) * (i + 1);
      canvas.drawLine(
        Offset(x, pillarRect.top),
        Offset(x, pillarRect.bottom),
        verticalGrainPaint,
      );
    }
    
    // Highlight on pillar
    final highlightPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.2)
      ..strokeWidth = 3;
    
    canvas.drawLine(
      Offset(pillarRect.left + pillarWidth * 0.2, pillarRect.top),
      Offset(pillarRect.left + pillarWidth * 0.2, pillarRect.bottom),
      highlightPaint,
    );
    
    // Pillar base
    final basePaint = Paint()
      ..color = const Color(0xFF4E342E);
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset(center.dx, pillarRect.bottom - 5),
          width: pillarWidth * 1.2,
          height: 10,
        ),
        const Radius.circular(5),
      ),
      basePaint,
    );
  }
  
  // Draw realistic tree
  static void drawRealisticTree(Canvas canvas, Offset position, double cellWidth, double cellHeight, {double size = 1.0}) {
    final center = Offset(
      position.dx * cellWidth + cellWidth / 2,
      position.dy * cellHeight + cellHeight / 2,
    );
    
    final treeSize = cellWidth * 0.9 * size;
    final trunkWidth = treeSize * 0.3;
    final trunkHeight = treeSize * 0.6;
    final crownRadius = treeSize * 0.5;
    
    // Tree shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.4)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5);
    
    canvas.drawOval(
      Rect.fromCenter(
        center: center + const Offset(5, 8),
        width: treeSize * 1.3,
        height: treeSize * 0.4,
      ),
      shadowPaint,
    );
    
    // Tree trunk
    final trunkGradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        const Color(0xFF8D6E63), // Light brown
        const Color(0xFF6D4C41), // Medium brown
        const Color(0xFF5D4037), // Dark brown
      ],
    );
    
    final trunkPaint = Paint()
      ..shader = trunkGradient.createShader(
        Rect.fromCenter(
          center: Offset(center.dx, center.dy + crownRadius * 0.3),
          width: trunkWidth,
          height: trunkHeight,
        ),
      );
    
    // Draw trunk
    final trunkRect = Rect.fromCenter(
      center: Offset(center.dx, center.dy + crownRadius * 0.3),
      width: trunkWidth,
      height: trunkHeight,
    );
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(trunkRect, Radius.circular(trunkWidth / 4)),
      trunkPaint,
    );
    
    // Tree bark lines
    final barkPaint = Paint()
      ..color = const Color(0xFF4E342E).withValues(alpha: 0.6)
      ..strokeWidth = 1;
    
    for (int i = 0; i < 4; i++) {
      double y = trunkRect.top + (trunkRect.height / 5) * (i + 1);
      canvas.drawLine(
        Offset(trunkRect.left, y),
        Offset(trunkRect.right, y),
        barkPaint,
      );
    }
    
    // Tree crown - leaf layers
    final random = Random(position.dx.toInt() + position.dy.toInt());
    
    // Back layer - darker
    final backCrownPaint = Paint()
      ..color = const Color(0xFF2E7D32);
    
    for (int i = 0; i < 3; i++) {
      double offsetX = (random.nextDouble() - 0.5) * crownRadius * 0.4;
      double offsetY = (random.nextDouble() - 0.5) * crownRadius * 0.4;
      canvas.drawCircle(
        center + Offset(offsetX, offsetY - crownRadius * 0.2),
        crownRadius * (0.8 + random.nextDouble() * 0.4),
        backCrownPaint,
      );
    }
    
    // Middle layer
    final midCrownPaint = Paint()
      ..color = const Color(0xFF388E3C);
    
    for (int i = 0; i < 2; i++) {
      double offsetX = (random.nextDouble() - 0.5) * crownRadius * 0.3;
      double offsetY = (random.nextDouble() - 0.5) * crownRadius * 0.3;
      canvas.drawCircle(
        center + Offset(offsetX, offsetY - crownRadius * 0.1),
        crownRadius * (0.7 + random.nextDouble() * 0.3),
        midCrownPaint,
      );
    }
    
    // Front layer - lighter
    final frontCrownPaint = Paint()
      ..color = const Color(0xFF4CAF50);
    
    canvas.drawCircle(
      center + Offset(0, -crownRadius * 0.1),
      crownRadius,
      frontCrownPaint,
    );
    
    // Highlight on crown
    final crownHighlightPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.2);
    
    canvas.drawCircle(
      center + Offset(-crownRadius * 0.3, -crownRadius * 0.4),
      crownRadius * 0.3,
      crownHighlightPaint,
    );
    
    // Scattered leaves around tree
    final leafPaint = Paint()
      ..color = const Color(0xFF66BB6A);
    
    for (int i = 0; i < 5; i++) {
      double angle = random.nextDouble() * 2 * pi;
      double distance = crownRadius * (1.1 + random.nextDouble() * 0.3);
      Offset leafPos = center + Offset(
        cos(angle) * distance,
        sin(angle) * distance - crownRadius * 0.1,
      );
      
      canvas.drawCircle(leafPos, 3, leafPaint);
    }
  }
  
  // Draw all obstacles in level
  static void drawObstacles(Canvas canvas, List<ObstacleData> obstacles, double cellWidth, double cellHeight) {
    for (final obstacle in obstacles) {
      switch (obstacle.type) {
        case ObstacleType.rock:
          drawRealisticRock(canvas, obstacle.position, cellWidth, cellHeight, size: obstacle.size);
          break;
        case ObstacleType.pillar:
          drawRealisticPillar(canvas, obstacle.position, cellWidth, cellHeight, size: obstacle.size);
          break;
        case ObstacleType.tree:
          drawRealisticTree(canvas, obstacle.position, cellWidth, cellHeight, size: obstacle.size);
          break;
      }
    }
  }
  
  // Check collision with obstacles
  static bool checkCollisionWithObstacles(Offset snakeHeadPosition, List<ObstacleData> obstacles) {
    for (final obstacle in obstacles) {
      double distance = (snakeHeadPosition - obstacle.position).distance;
      double collisionRadius = 0.8 * obstacle.size; // Collision radius
      
      if (distance < collisionRadius) {
        return true; // Collision!
      }
    }
    return false; // No collision
  }
}
