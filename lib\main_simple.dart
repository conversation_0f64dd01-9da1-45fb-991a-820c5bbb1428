import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math';
import 'models/level_data.dart';
import 'screens/settings_screen.dart';
import 'screens/levels_screen.dart';
import 'screens/game_over_screen.dart';

void main() {
  runApp(const SnakeGameApp());
}

class SnakeGameApp extends StatelessWidget {
  const SnakeGameApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Arza Snake',
      theme: ThemeData(
        primarySwatch: Colors.green,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const MainMenuScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class MainMenuScreen extends StatefulWidget {
  const MainMenuScreen({super.key});

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen> {
  static GameSettings globalGameSettings = GameSettings();

  void _applySettings(Map<String, dynamic> settings) {
    setState(() {
      globalGameSettings = GameSettings(
        soundEnabled: settings['soundEnabled'] ?? true,
        vibrationEnabled: settings['vibrationEnabled'] ?? true,
        gameSpeed: settings['gameSpeed'] ?? 1.0,
        difficulty: settings['difficulty'] ?? 'Medium',
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF2E7D32),
              Color(0xFF1B5E20),
              Color(0xFF0D2818),
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Game Title
                const Text(
                  'ARZA SNAKE',
                  style: TextStyle(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        blurRadius: 10.0,
                        color: Colors.black54,
                        offset: Offset(2.0, 2.0),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 60),
                
                // Menu Buttons
                _buildMenuButton(
                  'Start Game',
                  Icons.play_arrow,
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => GameScreen(
                          gameSettings: globalGameSettings,
                        ),
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 20),
                
                _buildMenuButton(
                  'Levels',
                  Icons.layers,
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const LevelsScreen(),
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 20),
                
                _buildMenuButton(
                  'Settings',
                  Icons.settings,
                  () async {
                    final result = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SettingsScreen(),
                      ),
                    );
                    if (result != null) {
                      _applySettings(result);
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(String text, IconData icon, VoidCallback onPressed) {
    return Container(
      width: 250,
      height: 60,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white, size: 24),
            const SizedBox(width: 10),
            Text(
              text,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class GameScreen extends StatefulWidget {
  final LevelData? levelData;
  final GameSettings? gameSettings;

  const GameScreen({
    super.key,
    this.levelData,
    this.gameSettings,
  });

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  // Game constants
  static const int gridWidth = 30;
  static const int gridHeight = 20;

  // Game state
  List<Offset> snake = [];
  Offset food = const Offset(0, 0);
  Offset direction = const Offset(1, 0);
  bool isGameRunning = false;
  bool isGameOver = false;
  int score = 0;
  Timer? gameTimer;
  int gameSpeed = 150;

  // Game settings and level
  late GameSettings gameSettings;
  late LevelData currentLevel;

  @override
  void initState() {
    super.initState();
    
    // Initialize settings and level
    gameSettings = widget.gameSettings ?? GameSettings();
    currentLevel = widget.levelData ?? _getDefaultLevel();
    
    // Calculate game speed
    _calculateGameSpeed();
    
    // Initialize game
    _initializeGame();
  }

  @override
  void dispose() {
    gameTimer?.cancel();
    super.dispose();
  }

  LevelData _getDefaultLevel() {
    return LevelData(
      level: 1,
      name: 'Practice Level',
      description: 'Learn the basics',
      difficulty: 'Easy',
      speed: 150,
      obstacles: [],
      requiredScore: 0,
      isUnlocked: true,
    );
  }

  void _calculateGameSpeed() {
    int baseSpeed = currentLevel.speed;
    double speedMultiplier = gameSettings.gameSpeed;
    gameSpeed = (baseSpeed / speedMultiplier).round().clamp(80, 300);
  }

  void _initializeGame() {
    snake = [
      const Offset(15, 10),
      const Offset(14, 10),
      const Offset(13, 10),
    ];
    _generateFood();
    direction = const Offset(1, 0);
    score = 0;
    isGameOver = false;
    isGameRunning = false;
  }

  void _generateFood() {
    Random random = Random();
    Offset newFood;
    
    do {
      newFood = Offset(
        random.nextInt(gridWidth).toDouble(),
        random.nextInt(gridHeight).toDouble(),
      );
    } while (snake.contains(newFood) || _isObstacle(newFood));
    
    setState(() {
      food = newFood;
    });
  }

  bool _isObstacle(Offset position) {
    for (var obstacle in currentLevel.obstacles) {
      if ((position - obstacle.position).distance < 1.0) {
        return true;
      }
    }
    return false;
  }

  void _startGame() {
    if (isGameOver) {
      _initializeGame();
    }
    
    setState(() {
      isGameRunning = true;
    });
    
    gameTimer = Timer.periodic(Duration(milliseconds: gameSpeed), (timer) {
      _moveSnake();
    });
  }

  void _pauseGame() {
    setState(() {
      isGameRunning = false;
    });
    gameTimer?.cancel();
  }

  void _moveSnake() {
    if (!isGameRunning) return;

    setState(() {
      // Calculate new head position
      Offset newHead = Offset(
        snake.first.dx + direction.dx,
        snake.first.dy + direction.dy,
      );

      // Check wall collision
      if (newHead.dx < 0 || newHead.dx >= gridWidth ||
          newHead.dy < 0 || newHead.dy >= gridHeight) {
        _gameOver();
        return;
      }

      // Check self collision
      if (snake.contains(newHead)) {
        _gameOver();
        return;
      }

      // Check obstacle collision
      if (_isObstacle(newHead)) {
        _gameOver();
        return;
      }

      // Add new head
      snake.insert(0, newHead);

      // Check food collision
      if (newHead == food) {
        score += 10;
        _generateFood();
        
        // Play sound if enabled
        if (gameSettings.soundEnabled) {
          SystemSound.play(SystemSoundType.click);
        }
        
        // Vibrate if enabled
        if (gameSettings.vibrationEnabled) {
          HapticFeedback.lightImpact();
        }
      } else {
        // Remove tail if not eating
        snake.removeLast();
      }
    });
  }

  void _gameOver() {
    gameTimer?.cancel();
    setState(() {
      isGameRunning = false;
      isGameOver = true;
    });
    
    _showGameOverDialog();
  }

  void _showGameOverDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Game Over'),
        content: Text('Score: $score'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _initializeGame();
              setState(() {});
            },
            child: const Text('Play Again'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('Main Menu'),
          ),
        ],
      ),
    );
  }

  void _changeDirection(Offset newDirection) {
    // Prevent reverse direction
    if (direction.dx != 0 && newDirection.dx != 0) return;
    if (direction.dy != 0 && newDirection.dy != 0) return;
    
    setState(() {
      direction = newDirection;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1B5E20),
      body: GestureDetector(
        onPanUpdate: (details) {
          if (!isGameRunning) return;
          
          double dx = details.delta.dx;
          double dy = details.delta.dy;
          
          if (dx.abs() > dy.abs()) {
            // Horizontal movement
            _changeDirection(dx > 0 ? const Offset(1, 0) : const Offset(-1, 0));
          } else {
            // Vertical movement
            _changeDirection(dy > 0 ? const Offset(0, 1) : const Offset(0, -1));
          }
        },
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF2E7D32),
                Color(0xFF1B5E20),
                Color(0xFF0D2818),
              ],
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Score and controls
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Score: $score',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Row(
                        children: [
                          IconButton(
                            onPressed: isGameRunning ? _pauseGame : _startGame,
                            icon: Icon(
                              isGameRunning ? Icons.pause : Icons.play_arrow,
                              color: Colors.white,
                              size: 30,
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(
                              Icons.home,
                              color: Colors.white,
                              size: 30,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // Game area
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.green[100],
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.green[800]!, width: 3),
                    ),
                    child: CustomPaint(
                      painter: GamePainter(
                        snake: snake,
                        food: food,
                        obstacles: currentLevel.obstacles,
                        gridWidth: gridWidth,
                        gridHeight: gridHeight,
                      ),
                      size: Size.infinite,
                    ),
                  ),
                ),
                
                // Instructions
                if (!isGameRunning && !isGameOver)
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'Swipe to control the snake',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class GamePainter extends CustomPainter {
  final List<Offset> snake;
  final Offset food;
  final List<ObstacleData> obstacles;
  final int gridWidth;
  final int gridHeight;

  GamePainter({
    required this.snake,
    required this.food,
    required this.obstacles,
    required this.gridWidth,
    required this.gridHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    double cellWidth = size.width / gridWidth;
    double cellHeight = size.height / gridHeight;

    // Draw snake
    Paint snakePaint = Paint()..color = Colors.green[800]!;
    Paint headPaint = Paint()..color = Colors.green[900]!;
    
    for (int i = 0; i < snake.length; i++) {
      Offset segment = snake[i];
      Rect rect = Rect.fromLTWH(
        segment.dx * cellWidth,
        segment.dy * cellHeight,
        cellWidth,
        cellHeight,
      );
      
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(4)),
        i == 0 ? headPaint : snakePaint,
      );
    }

    // Draw food
    Paint foodPaint = Paint()..color = Colors.red;
    Rect foodRect = Rect.fromLTWH(
      food.dx * cellWidth,
      food.dy * cellHeight,
      cellWidth,
      cellHeight,
    );
    canvas.drawOval(foodRect, foodPaint);

    // Draw obstacles
    Paint obstaclePaint = Paint()..color = Colors.brown;
    for (var obstacle in obstacles) {
      Rect obstacleRect = Rect.fromLTWH(
        obstacle.position.dx * cellWidth,
        obstacle.position.dy * cellHeight,
        cellWidth,
        cellHeight,
      );
      canvas.drawRect(obstacleRect, obstaclePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}