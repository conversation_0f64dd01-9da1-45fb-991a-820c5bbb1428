import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../lib/utils/icon_generator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🎨 Generating default app icon...');
  
  try {
    // Generate icon
    final iconData = await IconGenerator.generateDefaultIcon(size: 1024);
    
    // Save to assets/icons/
    final file = File('assets/icons/app_icon.png');
    await file.writeAsBytes(iconData);
    
    print('✅ Default icon generated successfully: ${file.path}');
    print('📱 Icon size: 1024x1024 pixels');
    print('🎯 You can replace this with your custom icon');
    
    // Generate smaller sizes
    final sizes = [512, 256, 128, 72, 48];
    for (final size in sizes) {
      final smallIconData = await IconGenerator.generateDefaultIcon(size: size);
      final smallFile = File('assets/icons/app_icon_$size.png');
      await smallFile.writeAsBytes(smallIconData);
      print('✅ Generated: app_icon_$size.png');
    }
    
    print('\n🚀 All icons generated! Ready to build the app.');
    
  } catch (e) {
    print('❌ Error generating icon: $e');
    exit(1);
  }
}
