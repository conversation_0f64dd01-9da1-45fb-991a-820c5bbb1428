import 'package:flutter/services.dart';
import 'package:audioplayers/audioplayers.dart';

class SimpleSoundManager {
  static final AudioPlayer _audioPlayer = AudioPlayer();
  static bool _soundEnabled = true;
  
  // Initialize sound manager
  static Future<void> initialize({bool soundEnabled = true}) async {
    _soundEnabled = soundEnabled;
  }
  
  // Enable/disable sounds
  static void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
  }
  
  // Play a specific sound
  static Future<void> playSound(String soundType) async {
    if (!_soundEnabled) return;
    
    try {
      // Use system sounds for simplicity and reliability
      switch (soundType) {
        case 'eat':
          // Classic eating sound using system click
          SystemSound.play(SystemSoundType.click);
          HapticFeedback.lightImpact();
          // Add extra feedback to make it more noticeable
          Future.delayed(Duration(milliseconds: 50), () {
            HapticFeedback.selectionClick();
          });
          break;
        case 'gameOver':
          SystemSound.play(SystemSoundType.alert);
          HapticFeedback.heavyImpact();
          // Double alert for game over to make it more noticeable
          Future.delayed(Duration(milliseconds: 200), () {
            SystemSound.play(SystemSoundType.alert);
            HapticFeedback.heavyImpact();
          });
          break;
        case 'buttonClick':
          SystemSound.play(SystemSoundType.click);
          HapticFeedback.selectionClick();
          break;
        case 'fruit':
          SystemSound.play(SystemSoundType.click);
          HapticFeedback.selectionClick();
          break;
        case 'move':
          // Very subtle movement feedback (only occasionally)
          if (DateTime.now().millisecondsSinceEpoch % 20 == 0) {
            HapticFeedback.selectionClick();
          }
          break;
        case 'levelComplete':
          SystemSound.play(SystemSoundType.click);
          HapticFeedback.mediumImpact();
          break;
        case 'highScore':
          SystemSound.play(SystemSoundType.click);
          HapticFeedback.mediumImpact();
          break;
        case 'gameStart':
          SystemSound.play(SystemSoundType.click);
          HapticFeedback.lightImpact();
          break;
        default:
          SystemSound.play(SystemSoundType.click);
          break;
      }
    } catch (e) {
      // Fail silently to avoid crashes
      print('Sound play failed: $e');
    }
  }
  
  // Dispose resources
  static Future<void> dispose() async {
    try {
      await _audioPlayer.dispose();
    } catch (e) {
      print('Sound dispose failed: $e');
    }
  }
}