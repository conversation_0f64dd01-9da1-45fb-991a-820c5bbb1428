{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\ArzaSnake\\arzasnake\\android\\app\\.cxx\\RelWithDebInfo\\453pv5c4\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\ArzaSnake\\arzasnake\\android\\app\\.cxx\\RelWithDebInfo\\453pv5c4\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}